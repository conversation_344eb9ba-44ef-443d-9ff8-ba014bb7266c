# Convex configuration from your project dashboard
# https://dashboard.convex.dev/
NEXT_PUBLIC_CONVEX_URL=your-convex-deployment-url

# Clerk authentication keys from your Clerk dashboard  
# https://dashboard.clerk.com/
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key
CLERK_SECRET_KEY=your-clerk-secret-key

# Production URL for redirects
NEXT_PUBLIC_SITE_URL=https://tears.cjn.link

# OpenRouter API Key for AI image and text processing
OPENROUTER_API_KEY=your-openrouter-api-key

# Model Configuration
MODEL=openai/gpt-4o

# Image Processing Configuration  
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_TYPES=image/jpeg,image/png,image/webp

# Rate Limiting Configuration
USER_DAILY_LIMIT=5
GLOBAL_DAILY_LIMIT=1000