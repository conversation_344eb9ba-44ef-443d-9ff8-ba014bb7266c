# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
bun run build        # Build production application
bun run start        # Start production server
bun run lint         # Run ESLint
bunx convex dev      # Start Convex development server
```

## Architecture Overview

This is a Next.js 15 application with Convex as the backend and Clerk for authentication. The app features AI-powered image and text processing with built-in rate limiting and secure user management.

### Key Technologies
- **Next.js 15** with App Router and React 19
- **Convex** for serverless backend and real-time database
- **Clerk** for authentication and user management
- **TypeScript** in strict mode
- **Tailwind CSS** with shadcn/ui components
- **OpenRouter** for AI model access (GPT-4o)
- **next-themes** for dark mode support

### Authentication Flow
- Uses Clerk for authentication with both client and server-side access
- Middleware handles route protection
- Protected routes redirect to `/auth/login` if not authenticated
- User data stored in Convex with Clerk ID mapping

### Directory Structure
```
app/
├── auth/              # Authentication pages (login, signup, etc.)
├── editor/            # Protected main application interface
├── api/               # API routes for image/text processing
├── layout.tsx         # Root layout with Convex and Clerk providers
├── page.tsx           # Landing page with auth redirect
└── globals.css        # Global styles

components/
├── auth-button.tsx    # Login/logout button
├── login-form.tsx     # Clerk-powered login form
├── sign-up-form.tsx   # Clerk-powered signup form
└── ui/                # shadcn/ui components

convex/
├── schema.ts          # Database schema definition
├── auth.ts            # User management functions
├── generations.ts     # Generation logging and history
├── rateLimits.ts      # Atomic rate limiting system
├── storage.ts         # File storage functions
└── http.ts            # HTTP endpoints

lib/
├── convex.ts          # Convex client configuration
├── envConfig.ts       # Environment variable validation
└── utils.ts           # Utility functions

hooks/
├── useConvex.ts       # Custom Convex React hooks
└── useImageStorage.ts # Local image storage hooks
```

### Configuration Files
- `convex/schema.ts`: Database schema with proper indexing
- `components.json`: shadcn/ui configuration (New York style, CSS variables)
- `tailwind.config.ts`: Tailwind with shadcn/ui color system
- `next.config.ts`: Security headers and CSP configuration
- `eslint.config.mjs`: ESLint with Next.js and TypeScript rules

### Environment Variables Required
```env
# Convex
NEXT_PUBLIC_CONVEX_URL=your_convex_deployment_url

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key

# AI Processing
OPENROUTER_API_KEY=your_openrouter_api_key
MODEL=openai/gpt-4o

# Site Configuration
NEXT_PUBLIC_SITE_URL=your_site_url
```

## Code Patterns

### Convex Usage
- **Client components**: Use hooks from `@/hooks/useConvex.ts`
- **Server components**: Use ConvexHttpClient for mutations
- **Mutations**: Always use for data writes and rate limit checks
- **Queries**: Use for data reads with proper caching
- Always use proper TypeScript types with `Id<'tableName'>`

### Authentication Check Pattern
```typescript
// Server Components
const { userId } = await auth();
if (!userId) {
  redirect("/auth/login");
}

// Client Components  
const { user } = useUser();
const { signOut } = useClerk();
```

### Convex Integration Pattern
```typescript
// Custom hooks
export const useUserGenerations = (limit?: number) => {
  const { user } = useUser();
  return useQuery(
    api.generations.getUserGenerationHistory,
    user?.id ? { clerkUserId: user.id, limit } : 'skip'
  );
};

// Mutations with proper error handling
const logGeneration = useMutation(api.generations.logGenerationAttempt);
```

### Component Structure
- Uses React Server Components by default
- shadcn/ui components in `/components/ui/`
- Custom components follow TypeScript functional component pattern
- Proper use of `next/font` with Geist font

### Route Protection
- Middleware runs on protected routes using Clerk
- Redirects unauthenticated users to `/auth/login`
- Protected routes are in `/app/editor/` directory

## Important Implementation Notes

### Convex Integration
- Uses file-based routing for functions (auth.ts, generations.ts, etc.)
- Atomic operations for rate limiting to prevent race conditions
- Proper indexing for query performance
- Real-time subscriptions for live data updates

### Security Configuration
- CSP headers include Convex and Clerk domains
- Rate limiting at both user and global levels
- Input sanitization and validation
- Encrypted local storage for images

### UI System
- shadcn/ui with "new-york" style and CSS variables
- Dark mode support via `next-themes`
- Tailwind with custom color system and animations
- Lucide icons for UI elements

### Development Best Practices
- TypeScript strict mode enabled
- ESLint with Next.js and TypeScript rules
- Proper error handling in auth and API flows
- Server-side rendering with proper hydration
- Atomic database operations for consistency

## Database Schema

### Core Tables
- `users`: User profiles linked to Clerk IDs
- `user_mappings`: Maps Clerk IDs to numeric IDs for compatibility
- `generation_logs`: Comprehensive logging of all AI generations
- `rate_limits`: User-specific and global rate limiting
- `file_uploads`: File upload tracking and metadata

### Key Patterns
- All tables include proper indexes for query performance
- Use of `v.id('tableName')` for proper type safety
- Atomic operations for rate limiting
- Optional fields use `v.optional()` wrapper

## Memories and Notes

- Migration completed from Supabase to Convex + Clerk
- All Supabase references have been archived in `archive/supabase-old/`
- Rate limiting uses atomic operations to prevent race conditions
- Don't run `bunx convex dev` automatically - ask user when needed
- Follow Convex best practices from `docs/CONVEX_RULES.md`

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.