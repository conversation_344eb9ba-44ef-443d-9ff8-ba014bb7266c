"use client";

import { cn } from "@/lib/utils";
import { SignIn } from "@clerk/nextjs";

export function LoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <SignIn 
        routing="hash" 
        signUpUrl="/auth/sign-up"
        fallbackRedirectUrl="/editor"
        appearance={{
          elements: {
            rootBox: "mx-auto",
            card: "shadow-none border",
          }
        }}
      />
    </div>
  );
}