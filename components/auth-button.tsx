import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import { auth, currentUser } from "@clerk/nextjs/server";
import { LogoutButton } from "./logout-button";

export async function AuthButton() {
  const { userId } = await auth();
  const user = await currentUser();

  return userId ? (
    <div className="flex items-center gap-4">
      Hey, {user?.emailAddresses?.[0]?.emailAddress || user?.firstName || 'User'}!
      <LogoutButton />
    </div>
  ) : (
    <div className="flex gap-2">
      <Button asChild size="sm" variant={"outline"}>
        <Link href="/auth/login">Sign in</Link>
      </Button>
      <Button asChild size="sm" variant={"default"}>
        <Link href="/auth/sign-up">Sign up</Link>
      </Button>
    </div>
  );
}