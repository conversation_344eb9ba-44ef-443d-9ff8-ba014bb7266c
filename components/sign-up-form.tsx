"use client";

import { cn } from "@/lib/utils";
import { SignUp } from "@clerk/nextjs";

export function SignUpForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <SignUp 
        routing="hash" 
        signInUrl="/auth/login"
        fallbackRedirectUrl="/editor"
        appearance={{
          elements: {
            rootBox: "mx-auto",
            card: "shadow-none border",
          }
        }}
      />
    </div>
  );
}
