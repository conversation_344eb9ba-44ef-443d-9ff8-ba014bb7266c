import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/nextjs';
import { api } from '@/convex/_generated/api';

// Hook for getting user generation history
export const useUserGenerations = (limit?: number) => {
  const { user } = useUser();
  
  return useQuery(
    api.generations.getUserGenerationHistory,
    user?.id ? { clerkUserId: user.id, limit } : 'skip'
  );
};

// Hook for getting user generation stats
export const useGenerationStats = () => {
  const { user } = useUser();
  
  return useQuery(
    api.generations.getGenerationStats,
    user?.id ? { clerkUserId: user.id } : 'skip'
  );
};

// Hook for checking rate limits (returns a function to check if generation is allowed)
export const useRateLimit = (clerkUserId?: string, numericUserId?: number) => {
  return useQuery(
    api.rateLimits.checkGenerationAllowed,
    clerkUserId && numericUserId ? { clerkUserId, numericUserId } : 'skip'
  );
};

// Hook for logging generation attempts
export const useLogGeneration = () => {
  return useMutation(api.generations.logGenerationAttempt);
};