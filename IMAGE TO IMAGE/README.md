# IMAGE TO IMAGE - File Collection

This folder contains all the relevant files for the Image-to-Image generation feature in CreatorBuddy.

## 📁 Folder Structure

```
IMAGE TO IMAGE/
├── frontend/
│   ├── app/thumbnails/
│   │   └── page.tsx                    # Main thumbnails page with I2I interface
│   └── components/
│       ├── image-upload.tsx           # Image upload component with validation
│       └── image-preview-modal.tsx    # Modal for previewing generated images
├── backend/convex/
│   ├── thumbnails.ts                  # Core I2I generation logic & API
│   └── schema.ts                      # Database schema for thumbnails
├── docs/
│   └── IMAGE_TO_IMAGE_GENERATION.md   # Complete technical documentation
└── README.md                          # This file
```

## 📋 File Descriptions

### Frontend Files

**`frontend/app/thumbnails/page.tsx`**
- Main page component orchestrating the image-to-image workflow
- Handles form submission, file conversion, and progress tracking
- Contains both text-to-image and image-to-image generation modes

**`frontend/components/image-upload.tsx`**
- Specialized drag-and-drop image upload component
- File validation (JPEG/PNG/WebP, max 10MB)
- Preview functionality with metadata display

**`frontend/components/image-preview-modal.tsx`**
- Modal component for viewing generated thumbnails
- Navigation between multiple generated images
- Download and zoom functionality

### Backend Files

**`backend/convex/thumbnails.ts`**
- Core backend logic for image-to-image generation
- Google AI SDK integration with Gemini 2.5 Flash
- File storage management and database operations
- Error handling and status tracking

**`backend/convex/schema.ts`**
- Database schema definitions for thumbnails table
- Fields for source images, generated images, and metadata
- Indexes for efficient querying

### Documentation

**`docs/IMAGE_TO_IMAGE_GENERATION.md`**
- Comprehensive technical documentation
- Architecture diagrams and data flow explanations
- API integration details and troubleshooting guide

## 🔗 Original File Locations

All files in this collection are **COPIES** from the main project:

- `frontend/app/thumbnails/page.tsx` → `apps/web/src/app/thumbnails/page.tsx`
- `frontend/components/image-upload.tsx` → `apps/web/src/components/image-upload.tsx`
- `frontend/components/image-preview-modal.tsx` → `apps/web/src/components/image-preview-modal.tsx`
- `backend/convex/thumbnails.ts` → `packages/backend/convex/thumbnails.ts`
- `backend/convex/schema.ts` → `packages/backend/convex/schema.ts`
- `docs/IMAGE_TO_IMAGE_GENERATION.md` → `docs/IMAGE_TO_IMAGE_GENERATION.md`

## 🚀 How It Works

1. **User uploads image** via `image-upload.tsx` component
2. **File is validated** and converted to ArrayBuffer
3. **Frontend calls** `generateImageToImage` action in `thumbnails.ts`
4. **Backend stores** source image in Convex storage
5. **Google AI processes** image + text prompt via Gemini 2.5 Flash
6. **Generated image** is stored and database is updated
7. **Frontend displays** result via polling and preview modal

## 📦 Dependencies Required

- `@ai-sdk/google` - Google AI SDK
- `ai` - Vercel AI SDK
- `convex` - Backend platform
- `GOOGLE_GENERATIVE_AI_API_KEY` environment variable

## 🔄 Last Updated

Created: December 2024
Contains: Complete image-to-image generation system files