import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
	todos: defineTable({
		text: v.string(),
		completed: v.boolean(),
	}),
	thumbnails: defineTable({
		prompt: v.string(),
		fileId: v.optional(v.id("_storage")), // Generated image storage ID
		mediaType: v.string(), // e.g. "image/png"
		generatedAt: v.number(), // timestamp
		aiResponse: v.optional(v.string()), // AI's text response
		status: v.union(v.literal("generating"), v.literal("completed"), v.literal("failed")),
		errorMessage: v.optional(v.string()),
		// Image-to-image fields
		sourceImageId: v.optional(v.id("_storage")), // Source image for image-to-image generation
		generationType: v.optional(v.union(v.literal("text-to-image"), v.literal("image-to-image"))),
	}).index("by_status", ["status"]).index("by_generated_at", ["generatedAt"]).index("by_generation_type", ["generationType"]),
});
