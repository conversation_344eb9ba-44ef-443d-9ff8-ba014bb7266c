import { action, mutation, query, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { v } from "convex/values";
import { google } from '@ai-sdk/google';
import { generateText } from 'ai';

// Get all thumbnails, most recent first
export const getAll = query({
  returns: v.array(
    v.object({
      _id: v.id("thumbnails"),
      _creationTime: v.number(),
      prompt: v.string(),
      fileId: v.optional(v.id("_storage")),
      mediaType: v.string(),
      generatedAt: v.number(),
      aiResponse: v.optional(v.string()),
      status: v.union(v.literal("generating"), v.literal("completed"), v.literal("failed")),
      errorMessage: v.optional(v.string()),
      sourceImageId: v.optional(v.id("_storage")),
      generationType: v.optional(v.union(v.literal("text-to-image"), v.literal("image-to-image"))),
      imageUrl: v.optional(v.string()),
      sourceImageUrl: v.optional(v.string()),
    })
  ),
  handler: async (ctx) => {
    const thumbnails = await ctx.db
      .query("thumbnails")
      .withIndex("by_generated_at")
      .order("desc")
      .collect();
    
    // Add image URLs for completed thumbnails and source images
    return await Promise.all(
      thumbnails.map(async (thumbnail) => {
        const result: any = { ...thumbnail };
        
        // Add generated image URL if completed
        if (thumbnail.status === "completed" && thumbnail.fileId) {
          const imageUrl = await ctx.storage.getUrl(thumbnail.fileId);
          result.imageUrl = imageUrl;
        }
        
        // Add source image URL for image-to-image
        if (thumbnail.sourceImageId) {
          const sourceImageUrl = await ctx.storage.getUrl(thumbnail.sourceImageId);
          result.sourceImageUrl = sourceImageUrl;
        }
        
        return result;
      })
    );
  },
});

// Get thumbnails by status
export const getByStatus = query({
  args: {
    status: v.union(v.literal("generating"), v.literal("completed"), v.literal("failed")),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("thumbnails")
      .withIndex("by_status", (q) => q.eq("status", args.status))
      .order("desc")
      .collect();
  },
});

// Create a new thumbnail record for text-to-image (internal - only called from actions)
export const create = internalMutation({
  args: {
    prompt: v.string(),
  },
  returns: v.id("thumbnails"),
  handler: async (ctx, args) => {
    const thumbnailId = await ctx.db.insert("thumbnails", {
      prompt: args.prompt,
      mediaType: "",
      generatedAt: Date.now(),
      status: "generating",
      generationType: "text-to-image",
    });
    return thumbnailId;
  },
});

// Create a new thumbnail record for image-to-image (internal - only called from actions)
export const createImageToImage = internalMutation({
  args: {
    prompt: v.string(),
    sourceImageId: v.id("_storage"),
  },
  returns: v.id("thumbnails"),
  handler: async (ctx, args) => {
    const thumbnailId = await ctx.db.insert("thumbnails", {
      prompt: args.prompt,
      mediaType: "",
      generatedAt: Date.now(),
      status: "generating",
      generationType: "image-to-image",
      sourceImageId: args.sourceImageId,
    });
    return thumbnailId;
  },
});

// Update thumbnail with generated image (internal)
export const updateWithImage = internalMutation({
  args: {
    id: v.id("thumbnails"),
    fileId: v.id("_storage"),
    mediaType: v.string(),
    aiResponse: v.optional(v.string()),
  },
  returns: v.object({
    success: v.boolean(),
  }),
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, {
      fileId: args.fileId,
      mediaType: args.mediaType,
      aiResponse: args.aiResponse,
      status: "completed",
    });
    return { success: true };
  },
});

// Update thumbnail with error (internal)
export const updateWithError = internalMutation({
  args: {
    id: v.id("thumbnails"),
    errorMessage: v.string(),
  },
  returns: v.object({
    success: v.boolean(),
  }),
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, {
      status: "failed",
      errorMessage: args.errorMessage,
    });
    return { success: true };
  },
});


// Delete a thumbnail
export const deleteThumbnail = mutation({
  args: {
    id: v.id("thumbnails"),
  },
  returns: v.object({
    success: v.boolean(),
  }),
  handler: async (ctx, args) => {
    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Get individual thumbnail with source image URL
export const getThumbnailById = query({
  args: {
    id: v.id("thumbnails"),
  },
  returns: v.union(
    v.object({
      _id: v.id("thumbnails"),
      _creationTime: v.number(),
      prompt: v.string(),
      fileId: v.optional(v.id("_storage")),
      mediaType: v.string(),
      generatedAt: v.number(),
      aiResponse: v.optional(v.string()),
      status: v.union(v.literal("generating"), v.literal("completed"), v.literal("failed")),
      errorMessage: v.optional(v.string()),
      sourceImageId: v.optional(v.id("_storage")),
      generationType: v.optional(v.union(v.literal("text-to-image"), v.literal("image-to-image"))),
      imageUrl: v.optional(v.string()),
      sourceImageUrl: v.optional(v.string()),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    const thumbnail = await ctx.db.get(args.id);
    if (!thumbnail) {
      return null;
    }

    const result = { ...thumbnail };
    
    // Add generated image URL if completed
    if (thumbnail.status === "completed" && thumbnail.fileId) {
      const imageUrl = await ctx.storage.getUrl(thumbnail.fileId);
      (result as any).imageUrl = imageUrl;
    }
    
    // Add source image URL for image-to-image
    if (thumbnail.sourceImageId) {
      const sourceImageUrl = await ctx.storage.getUrl(thumbnail.sourceImageId);
      (result as any).sourceImageUrl = sourceImageUrl;
    }

    return result;
  },
});

// Generate thumbnail using AI SDK (Action)
export const generateThumbnail = action({
  args: {
    prompt: v.string(),
  },
  returns: v.object({
    success: v.boolean(),
    thumbnailId: v.id("thumbnails"),
    message: v.optional(v.string()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, args) => {
    "use node";
    
    // Check for Google AI API key
    const googleAIKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    
    if (!googleAIKey) {
      throw new Error("GOOGLE_GENERATIVE_AI_API_KEY environment variable is required");
    }

    // Create thumbnail record first
    const thumbnailId: Id<"thumbnails"> = await ctx.runMutation(
      internal.thumbnails.create,
      {
        prompt: args.prompt,
      }
    );

    // Enhanced prompt for better image generation
    const enhancedPrompt = `Create a high-quality, vibrant thumbnail image for: ${args.prompt}. Make it eye-catching, professional, and suitable for social media or video platforms. Use bright colors, clear composition, and engaging visual elements.`;

    try {
      console.log("🔵 Generating image with Google AI SDK...");
      
      const result = await generateText({
        model: google('gemini-2.5-flash-image-preview'),
        providerOptions: {
          google: { responseModalities: ['TEXT', 'IMAGE'] },
        },
        prompt: enhancedPrompt,
      });

      console.log("✅ Google AI SDK generation successful");

      // Check if we got any images
      if (result.files && result.files.length > 0) {
        const imageFile = result.files.find(file => file.mediaType.startsWith('image/'));
        
        if (imageFile) {
          console.log(`📸 Generated image: ${imageFile.mediaType}, ${imageFile.uint8Array.length} bytes`);
          
          // Store image in Convex file storage
          const arrayBuffer = imageFile.uint8Array.slice().buffer as ArrayBuffer;
          const blob = new Blob([arrayBuffer], { type: imageFile.mediaType });
          const fileId = await ctx.storage.store(blob);
          
          console.log(`💾 Stored image in Convex storage with ID: ${fileId}`);
          
          // Update thumbnail with file ID
          await ctx.runMutation(
            internal.thumbnails.updateWithImage,
            {
              id: thumbnailId,
              fileId: fileId,
              mediaType: imageFile.mediaType,
              aiResponse: result.text,
            }
          );

          return {
            success: true,
            thumbnailId,
            message: "Thumbnail generated successfully using Google AI",
          };
        } else {
          throw new Error("No image files found in the response from Google AI");
        }
      } else {
        throw new Error("No files generated by Google AI");
      }

    } catch (error) {
      // Update thumbnail with error
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      await ctx.runMutation(internal.thumbnails.updateWithError, {
        id: thumbnailId,
        errorMessage,
      });

      return {
        success: false,
        thumbnailId,
        error: errorMessage,
      };
    }
  },
});

// Generate image-to-image thumbnail using AI SDK (Action)
export const generateImageToImage = action({
  args: {
    prompt: v.string(),
    sourceImageFile: v.bytes(), // Source image as bytes
    sourceImageType: v.string(), // Source image MIME type
  },
  returns: v.object({
    success: v.boolean(),
    thumbnailId: v.id("thumbnails"),
    message: v.optional(v.string()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, args): Promise<{
    success: boolean;
    thumbnailId: Id<"thumbnails">;
    message?: string;
    error?: string;
  }> => {
    "use node";
    
    // Check for Google AI API key
    const googleAIKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    
    if (!googleAIKey) {
      throw new Error("GOOGLE_GENERATIVE_AI_API_KEY environment variable is required");
    }

    let thumbnailId: Id<"thumbnails"> | undefined;

    try {
      // Convert ArrayBuffer to Uint8Array for validation and processing
      const sourceImageBytes = new Uint8Array(args.sourceImageFile);
      
      // Validate input
      if (!sourceImageBytes || sourceImageBytes.length === 0) {
        throw new Error("No source image file provided");
      }

      if (!args.sourceImageType || !args.sourceImageType.startsWith('image/')) {
        throw new Error("Invalid source image type");
      }

      console.log(`📥 Processing image-to-image request: ${sourceImageBytes.length} bytes, type: ${args.sourceImageType}`);
      
      // Validate that we have proper image data (not logging the actual array)
      if (sourceImageBytes.length < 100) {
        console.warn('⚠️ Very small image file detected, might be corrupted');
      }

      // First, store the source image
      const sourceBlob = new Blob([args.sourceImageFile], { type: args.sourceImageType });
      const sourceImageId = await ctx.storage.store(sourceBlob);
      
      console.log(`📤 Stored source image with ID: ${sourceImageId}`);

      // Create thumbnail record for image-to-image
      thumbnailId = await ctx.runMutation(
        internal.thumbnails.createImageToImage,
        {
          prompt: args.prompt,
          sourceImageId: sourceImageId,
        }
      );

      console.log("🔵 Generating image-to-image with Google AI SDK...");
      
      const result = await generateText({
        model: google('gemini-2.5-flash-image-preview'),
        providerOptions: {
          google: { responseModalities: ['TEXT', 'IMAGE'] },
        },
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: args.prompt,
              },
              {
                type: 'image',
                image: sourceImageBytes,
              },
            ],
          },
        ],
      });

      console.log("✅ Google AI SDK image-to-image generation successful");

      // Check if we got any images
      if (result.files && result.files.length > 0) {
        const imageFile = result.files.find(file => file.mediaType.startsWith('image/'));
        
        if (imageFile) {
          console.log(`📸 Generated image: ${imageFile.mediaType}, ${imageFile.uint8Array.length} bytes`);
          
          // Store generated image in Convex file storage
          const arrayBuffer = imageFile.uint8Array.slice().buffer as ArrayBuffer;
          const blob = new Blob([arrayBuffer], { type: imageFile.mediaType });
          const fileId = await ctx.storage.store(blob);
          
          console.log(`💾 Stored generated image in Convex storage with ID: ${fileId}`);
          
          // Update thumbnail with file ID
          await ctx.runMutation(
            internal.thumbnails.updateWithImage,
            {
              id: thumbnailId,
              fileId: fileId,
              mediaType: imageFile.mediaType,
              aiResponse: result.text,
            }
          );

          return {
            success: true,
            thumbnailId: thumbnailId!,
            message: "Image-to-image thumbnail generated successfully using Google AI",
          };
        } else {
          throw new Error("No image files found in the response from Google AI");
        }
      } else {
        throw new Error("No files generated by Google AI");
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ Image-to-image generation failed:", errorMessage);
      console.error("❌ Full error details:", error);
      
      // Check if error involves serialization issues
      if (errorMessage.includes('circular') || errorMessage.includes('serialize')) {
        console.error("⚠️ Possible serialization issue detected");
      }
      
      // If we created a thumbnailId, update it with error
      if (thumbnailId) {
        await ctx.runMutation(internal.thumbnails.updateWithError, {
          id: thumbnailId,
          errorMessage,
        });
        
        return {
          success: false,
          thumbnailId: thumbnailId!,
          error: errorMessage,
        };
      }
      
      // If no thumbnailId was created, we need to create a minimal one for tracking
      const fallbackThumbnailId: Id<"thumbnails"> = await ctx.runMutation(internal.thumbnails.create, {
        prompt: args.prompt,
      });
      
      await ctx.runMutation(internal.thumbnails.updateWithError, {
        id: fallbackThumbnailId,
        errorMessage,
      });
      
      return {
        success: false,
        thumbnailId: fallbackThumbnailId,
        error: errorMessage,
      };
    }
  },
});
