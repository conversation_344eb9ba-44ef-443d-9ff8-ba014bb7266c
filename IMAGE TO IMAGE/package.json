{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F @CreatorBuddy/backend dev", "dev:setup": "turbo -F @CreatorBuddy/backend dev:setup"}, "dependencies": {"@ai-sdk/google": "^2.0.11", "@openrouter/ai-sdk-provider": "^1.1.2", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-tabs": "^1.1.13", "ai": "^5.0.26"}, "devDependencies": {"turbo": "^2.5.4"}, "packageManager": "bun@1.2.19"}