"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Loader2, Download, Trash2, ImageIcon, AlertCircle, Palette, Upload } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { useAction, useMutation, useQuery } from "convex/react";
import { api } from "@CreatorBuddy/backend/convex/_generated/api";
import type { Id } from "@CreatorBuddy/backend/convex/_generated/dataModel";

import { ImageUpload } from "@/components/image-upload";
import { ImagePreviewModal } from "@/components/image-preview-modal";

export default function ThumbnailsPage() {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationType, setGenerationType] = useState<"text-to-image" | "image-to-image">("text-to-image");
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewThumbnail, setPreviewThumbnail] = useState<any>(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  const thumbnails = useQuery(api.thumbnails.getAll);
  const generateThumbnail = useAction(api.thumbnails.generateThumbnail);
  const generateImageToImage = useAction(api.thumbnails.generateImageToImage);
  const deleteThumbnail = useMutation(api.thumbnails.deleteThumbnail);

  const handleGenerate = async (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedPrompt = prompt.trim();
    if (!trimmedPrompt) return;

    if (generationType === "image-to-image" && !selectedImage) {
      toast.error("Please select a source image for image-to-image generation");
      return;
    }

    setIsGenerating(true);
    
    try {
      if (generationType === "text-to-image") {
        const result = await generateThumbnail({ prompt: trimmedPrompt });
        
        if (result.success) {
          toast.success("Thumbnail generated successfully!");
          setPrompt("");
        } else {
          toast.error(`Failed to generate thumbnail: ${result.error}`);
        }
      } else {
        // Image-to-image generation
        try {
          console.log("🔄 Starting image-to-image conversion...");
          console.log("📁 Source image details:", {
            name: selectedImage!.name,
            size: selectedImage!.size,
            type: selectedImage!.type
          });

          const sourceImageArrayBuffer = await selectedImage!.arrayBuffer();
          const sourceImageBytes = new Uint8Array(sourceImageArrayBuffer);
          
          console.log("✅ Image conversion successful:", {
            originalSize: selectedImage!.size,
            arrayBufferSize: sourceImageArrayBuffer.byteLength,
            uint8ArrayLength: sourceImageBytes.length
          });

          const result = await generateImageToImage({
            prompt: trimmedPrompt,
            // Convex v.bytes() expects an ArrayBuffer, not a Uint8Array
            sourceImageFile: sourceImageArrayBuffer,
            sourceImageType: selectedImage!.type,
          });
          
          console.log("🎯 Generation result:", {
            success: result.success,
            message: result.message,
            error: result.error ? String(result.error).substring(0, 200) + "..." : result.error,
            thumbnailId: result.thumbnailId
          });
          
          if (result.success) {
            toast.success("Image-to-image thumbnail generated successfully!");
            setPrompt("");
            setSelectedImage(null);
          } else {
            console.error("❌ Generation failed:", result.error);
            toast.error(`Failed to generate image-to-image thumbnail: ${result.error}`);
          }
        } catch (conversionError) {
          const msg = conversionError instanceof Error ? conversionError.message : "Unknown error";
          console.error("❌ Image preparation error:", msg);
          toast.error("Failed to process the uploaded image");
        }
      }
    } catch (error) {
      toast.error("An error occurred while generating the thumbnail");
      console.error("Main error:", error instanceof Error ? error.message : String(error));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = async (imageUrl: string, mediaType: string, prompt: string) => {
    const debug = new URLSearchParams(window.location.search).get("debug") === "1" || localStorage.getItem("cb_debug") === "1";
    try {
      if (debug) console.groupCollapsed("[DL] start", { imageUrl, mediaType });
      // Prefer direct link if same-origin and has correct extension
      const ext = (mediaType.split("/")[1] || "png").split(";")[0];
      const safeName = `thumbnail-${prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, "-")}-${Date.now()}.${ext}`;
      const response = await fetch(imageUrl, { cache: "no-store" });
      const ct = response.headers.get("content-type") || "";
      const cl = response.headers.get("content-length") || "";
      if (debug) console.debug("[DL] response", { status: response.status, ct, cl });
      const blob = await response.blob();
      if (debug) console.debug("[DL] blob", { type: blob.type, size: blob.size });
      // Inspect first 16 bytes
      const buf = await blob.arrayBuffer();
      const view = new Uint8Array(buf.slice(0, 16));
      if (debug) console.debug("[DL] head bytes (hex)", Array.from(view).map(b => b.toString(16).padStart(2, "0")).join(" "));
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.download = safeName;
      link.rel = "noopener";
      document.body.appendChild(link);
      link.click();
      link.remove();
      URL.revokeObjectURL(url);
      toast.success("Thumbnail downloaded!");
      if (debug) console.groupEnd();
    } catch (error) {
      console.error("[DL] error", error);
      toast.error("Failed to download thumbnail");
      // Fallback: try opening in new tab (lets browser handle it)
      try { window.open(imageUrl, "_blank", "noopener"); } catch {}
    }
  };

  const handleDelete = async (id: Id<"thumbnails">) => {
    try {
      await deleteThumbnail({ id });
      toast.success("Thumbnail deleted");
    } catch (error) {
      toast.error("Failed to delete thumbnail");
    }
  };

  const handleImagePreview = (thumbnail: any) => {
    setPreviewThumbnail(thumbnail);
    setPreviewOpen(true);
  };

  const handlePreviewNavigate = (direction: "prev" | "next") => {
    if (!thumbnails || !previewThumbnail) return;
    
    const currentIndex = thumbnails.findIndex(t => t._id === previewThumbnail._id);
    let newIndex;
    
    if (direction === "prev") {
      newIndex = currentIndex > 0 ? currentIndex - 1 : thumbnails.length - 1;
    } else {
      newIndex = currentIndex < thumbnails.length - 1 ? currentIndex + 1 : 0;
    }
    
    setPreviewThumbnail(thumbnails[newIndex]);
  };

  const completedThumbnails = thumbnails?.filter(t => t.status === "completed" && t.imageUrl) ?? [];

  return (
    <div className="container mx-auto max-w-6xl px-4 py-8">
      <div className="mb-8">
        <h1 className="type-1 mb-2">AI Thumbnail Generator</h1>
        <p className="type-3 text-paragraph/80">
          Generate eye-catching thumbnails using Gemini 2.5 Flash with text-to-image and image-to-image
        </p>
      </div>

      {/* Generation Form */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Create New Thumbnail</CardTitle>
          <CardDescription>
            Choose between text-to-image or image-to-image generation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={generationType} onValueChange={(value) => {
            setGenerationType(value as "text-to-image" | "image-to-image");
            setSelectedImage(null); // Clear selected image when switching tabs
          }}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="text-to-image" className="flex items-center gap-2">
                <ImageIcon className="h-4 w-4" />
                Text to Image
              </TabsTrigger>
              <TabsTrigger value="image-to-image" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Image to Image
              </TabsTrigger>
            </TabsList>
            
            <form onSubmit={handleGenerate} className="space-y-6">
              <TabsContent value="text-to-image" className="mt-0 space-y-4">
                <div>
                  <Label htmlFor="prompt">Thumbnail Description</Label>
                  <Input
                    id="prompt"
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="e.g., A cooking video thumbnail with pasta and tomato sauce"
                    className="mt-1"
                    disabled={isGenerating}
                  />
                </div>
              </TabsContent>

              <TabsContent value="image-to-image" className="mt-0 space-y-4">
                <div>
                  <Label htmlFor="source-image">Source Image</Label>
                  <div className="mt-1">
                    <ImageUpload
                      onImageSelect={setSelectedImage}
                      onImageClear={() => setSelectedImage(null)}
                      selectedImage={selectedImage}
                      disabled={isGenerating}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="prompt">Editing Instructions</Label>
                  <Input
                    id="prompt"
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="e.g., Change the blue sofa to a vintage brown leather chesterfield"
                    className="mt-1"
                    disabled={isGenerating}
                  />
                </div>
              </TabsContent>
              
              <Button 
                type="submit" 
                disabled={!prompt.trim() || isGenerating || (generationType === "image-to-image" && !selectedImage)}
                className="w-full sm:w-auto"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    {generationType === "text-to-image" ? (
                      <ImageIcon className="mr-2 h-4 w-4" />
                    ) : (
                      <Palette className="mr-2 h-4 w-4" />
                    )}
                    Generate Thumbnail
                  </>
                )}
              </Button>
            </form>
          </Tabs>
        </CardContent>
      </Card>

      {/* Thumbnails Grid */}
      <div>
        <h2 className="mb-4 text-xl font-semibold">Generated Thumbnails</h2>
        
        {thumbnails === undefined ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-foreground" />
          </div>
        ) : thumbnails.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <ImageIcon className="mb-4 h-12 w-12 text-foreground/40" />
              <p className="type-3 text-paragraph/80">No thumbnails generated yet.</p>
              <p className="type-4 text-paragraph/70">Create your first thumbnail above!</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {thumbnails.map((thumbnail) => (
              <Card key={thumbnail._id} className="overflow-hidden">
                <CardContent className="p-0">
                  {thumbnail.status === "completed" && thumbnail.imageUrl ? (
                    <div className="aspect-video relative group cursor-pointer" onClick={() => handleImagePreview(thumbnail)}>
                      <img
                        src={thumbnail.imageUrl}
                        alt={thumbnail.prompt}
                        className="w-full h-full object-cover transition-transform group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownload(thumbnail.imageUrl!, thumbnail.mediaType, thumbnail.prompt);
                          }}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(thumbnail._id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      {/* Generation Type Badge */}
                      <div className="absolute top-2 right-2">
                        <Badge variant={(thumbnail.generationType || "text-to-image") === "text-to-image" ? "default" : "secondary"} className="text-xs">
                          {(thumbnail.generationType || "text-to-image") === "text-to-image" ? (
                            <>
                              <ImageIcon className="h-3 w-3 mr-1" />
                              Text
                            </>
                          ) : (
                            <>
                              <Palette className="h-3 w-3 mr-1" />
                              Edit
                            </>
                          )}
                        </Badge>
                      </div>
                    </div>
                  ) : thumbnail.status === "generating" ? (
                    <div className="aspect-video bg-muted flex items-center justify-center">
                      <div className="text-center">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground">Generating...</p>
                      </div>
                    </div>
                  ) : thumbnail.status === "failed" ? (
                    <div className="aspect-video bg-muted flex items-center justify-center">
                      <div className="text-center">
                        <AlertCircle className="h-8 w-8 mx-auto mb-2 text-destructive" />
                        <p className="text-sm text-destructive">Generation Failed</p>
                        {thumbnail.errorMessage && (
                          <p className="type-4 text-paragraph/70 mt-1 px-2">
                            {thumbnail.errorMessage}
                          </p>
                        )}
                      </div>
                    </div>
                  ) : null}
                  
                  <div className="p-4">
                    <div className="flex items-start justify-between gap-2 mb-2">
                      <p className="type-4 font-semibold line-clamp-2 text-headline/90 flex-1">
                        {thumbnail.prompt}
                      </p>
                      <Badge variant={(thumbnail.generationType || "text-to-image") === "text-to-image" ? "default" : "secondary"} className="text-xs flex-shrink-0">
                        {(thumbnail.generationType || "text-to-image") === "text-to-image" ? "Text-to-Image" : "Image-to-Image"}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between type-4 text-paragraph/70">
                      <span>
                        {new Date(thumbnail.generatedAt).toLocaleDateString()}
                      </span>
                      <span className={`inline-flex items-center gap-2 rounded-full px-3 py-1 font-semibold ${
                        thumbnail.status === "completed"
                          ? "bg-primary/20 text-primary-foreground"
                          : thumbnail.status === "generating"
                          ? "bg-foreground/10 text-foreground"
                          : "bg-primary-foreground/15 text-primary-foreground"
                      }`}>
                        {thumbnail.status}
                      </span>
                    </div>
                    
                    {thumbnail.aiResponse && (
                      <p className="type-4 mt-2 italic line-clamp-2 text-paragraph/70">
                        "{thumbnail.aiResponse}"
                      </p>
                    )}

                    {/* Source Image Preview for Image-to-Image */}
                    {(thumbnail.generationType === "image-to-image") && thumbnail.sourceImageUrl && (
                      <div className="mt-3 pt-3 border-t">
                        <p className="type-4 text-paragraph/60 text-xs mb-1">Source:</p>
                        <div className="w-16 h-16 rounded overflow-hidden bg-muted">
                          <img
                            src={thumbnail.sourceImageUrl}
                            alt="Source image"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Image Preview Modal */}
      <ImagePreviewModal
        isOpen={previewOpen}
        onOpenChange={setPreviewOpen}
        thumbnail={previewThumbnail}
        allThumbnails={completedThumbnails}
        onNavigate={handlePreviewNavigate}
      />
    </div>
  );
}
