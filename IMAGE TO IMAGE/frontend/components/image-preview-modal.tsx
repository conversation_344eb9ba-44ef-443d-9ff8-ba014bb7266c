"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Download, 
  Copy, 
  ChevronLeft, 
  ChevronRight, 
  ZoomIn, 
  ZoomOut,
  RotateCcw,
  ImageIcon,
  Palette
} from "lucide-react";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import type { Id } from "@CreatorBuddy/backend/convex/_generated/dataModel";

type ThumbnailType = {
  _id: Id<"thumbnails">;
  prompt: string;
  mediaType: string;
  generatedAt: number;
  imageUrl?: string;
  sourceImageUrl?: string;
  generationType?: "text-to-image" | "image-to-image";
  status: "generating" | "completed" | "failed";
  aiResponse?: string;
} | null;

interface ImagePreviewModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  thumbnail: ThumbnailType;
  allThumbnails?: Array<ThumbnailType>;
  onNavigate?: (direction: "prev" | "next") => void;
}

export function ImagePreviewModal({ 
  isOpen, 
  onOpenChange, 
  thumbnail, 
  allThumbnails,
  onNavigate 
}: ImagePreviewModalProps) {
  const [zoom, setZoom] = useState(1);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setZoom(1);
      setImageLoaded(false);
    }
  }, [isOpen, thumbnail?._id]);

  if (!thumbnail) return null;

  const handleDownload = async () => {
    if (!thumbnail.imageUrl) return;
    
    try {
      const response = await fetch(thumbnail.imageUrl);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement("a");
      link.href = url;
      const filename = `thumbnail-${thumbnail.prompt.slice(0, 30).replace(/[^a-zA-Z0-9]/g, "-")}-${Date.now()}.${thumbnail.mediaType.split("/")[1] || "png"}`;
      link.download = filename;
      link.click();
      
      URL.revokeObjectURL(url);
      toast.success("Image downloaded!");
    } catch (error) {
      toast.error("Failed to download image");
    }
  };

  const handleCopyUrl = async () => {
    if (!thumbnail.imageUrl) return;
    
    try {
      await navigator.clipboard.writeText(thumbnail.imageUrl);
      toast.success("Image URL copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy URL");
    }
  };

  const handleZoomIn = () => setZoom(prev => Math.min(prev * 1.2, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev / 1.2, 0.5));
  const handleResetZoom = () => setZoom(1);

  const canNavigate = allThumbnails && allThumbnails.length > 1;
  
  const currentIndex = allThumbnails?.findIndex(t => t?._id === thumbnail._id) ?? -1;
  const canGoPrev = canNavigate && currentIndex > 0;
  const canGoNext = canNavigate && currentIndex < (allThumbnails?.length ?? 0) - 1;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl w-full h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <DialogTitle className="type-2 text-headline/90 line-clamp-2 pr-4">
                {thumbnail.prompt}
              </DialogTitle>
              <DialogDescription className="type-4 text-paragraph/70 mt-1">
                Generated on {new Date(thumbnail.generatedAt).toLocaleDateString()} • {thumbnail.mediaType}
              </DialogDescription>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              <Badge variant={(thumbnail.generationType || "text-to-image") === "text-to-image" ? "default" : "secondary"}>
                {(thumbnail.generationType || "text-to-image") === "text-to-image" ? (
                  <>
                    <ImageIcon className="h-3 w-3 mr-1" />
                    Text-to-Image
                  </>
                ) : (
                  <>
                    <Palette className="h-3 w-3 mr-1" />
                    Image-to-Image
                  </>
                )}
              </Badge>
            </div>
          </div>
        </DialogHeader>

        {/* Image Container */}
        <div className="flex-1 relative overflow-hidden bg-muted rounded-lg min-h-0">
          {thumbnail.imageUrl ? (
            <div className="w-full h-full flex items-center justify-center p-4">
              <img
                src={thumbnail.imageUrl}
                alt={thumbnail.prompt}
                className="max-w-full max-h-full object-contain transition-transform duration-200 cursor-zoom-in"
                style={{ transform: `scale(${zoom})` }}
                onLoad={() => setImageLoaded(true)}
                onClick={zoom === 1 ? handleZoomIn : handleResetZoom}
              />
              
              {/* Zoom Controls */}
              {imageLoaded && (
                <div className="absolute bottom-4 right-4 flex gap-1 bg-background/80 backdrop-blur-sm rounded-lg p-1 shadow-lg">
                  <Button size="sm" variant="ghost" onClick={handleZoomOut} disabled={zoom <= 0.5}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="ghost" onClick={handleResetZoom}>
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="ghost" onClick={handleZoomIn} disabled={zoom >= 3}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center">
                <ImageIcon className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
                <p className="type-3 text-muted-foreground">No image available</p>
              </div>
            </div>
          )}

          {/* Navigation Controls */}
          {canNavigate && (
            <>
              <Button
                size="sm"
                variant="ghost"
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm shadow-lg"
                onClick={() => onNavigate?.("prev")}
                disabled={!canGoPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm shadow-lg"
                onClick={() => onNavigate?.("next")}
                disabled={!canGoNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>

        {/* Source Image for Image-to-Image */}
        {(thumbnail.generationType === "image-to-image") && thumbnail.sourceImageUrl && (
          <div className="flex-shrink-0 mt-4">
            <h4 className="type-4 font-semibold text-headline/90 mb-2">Source Image</h4>
            <div className="w-32 h-32 rounded-lg overflow-hidden bg-muted">
              <img
                src={thumbnail.sourceImageUrl}
                alt="Source image"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        )}

        {/* AI Response */}
        {thumbnail.aiResponse && (
          <div className="flex-shrink-0 mt-4 p-3 bg-muted/50 rounded-lg">
            <p className="type-4 text-paragraph/80 italic">"{thumbnail.aiResponse}"</p>
          </div>
        )}

        {/* Actions */}
        <div className="flex-shrink-0 flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-2">
            {canNavigate && (
              <p className="type-4 text-paragraph/70">
                {currentIndex + 1} of {allThumbnails?.length}
              </p>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={handleCopyUrl} disabled={!thumbnail.imageUrl}>
              <Copy className="h-4 w-4 mr-1" />
              Copy URL
            </Button>
            <Button size="sm" onClick={handleDownload} disabled={!thumbnail.imageUrl}>
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}