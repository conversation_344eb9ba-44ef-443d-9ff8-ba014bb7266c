"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, X, ImageIcon } from "lucide-react";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface ImageUploadProps {
  onImageSelect: (file: File) => void;
  onImageClear: () => void;
  selectedImage: File | null;
  disabled?: boolean;
}

export function ImageUpload({ onImageSelect, onImageClear, selectedImage, disabled }: ImageUploadProps) {
  const [dragActive, setDragActive] = useState(false);

  const validateFile = useCallback((file: File) => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error("Please select a JPG, PNG, or WebP image");
      return false;
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      toast.error("Image must be less than 10MB");
      return false;
    }

    return true;
  }, []);

  const handleFiles = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    if (validateFile(file)) {
      onImageSelect(file);
      toast.success("Image selected successfully");
    }
  }, [validateFile, onImageSelect]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (disabled) return;
    
    if (e.dataTransfer.files) {
      handleFiles(e.dataTransfer.files);
    }
  }, [disabled, handleFiles]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
  }, [handleFiles]);

  return (
    <div className="w-full">
      {selectedImage ? (
        <Card className="relative">
          <CardContent className="p-0">
            <div className="relative aspect-video">
              <img
                src={URL.createObjectURL(selectedImage)}
                alt="Selected image"
                className="w-full h-full object-cover rounded-lg"
              />
              <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={onImageClear}
                  disabled={disabled}
                  className="bg-destructive/80 hover:bg-destructive"
                >
                  <X className="h-4 w-4 mr-1" />
                  Remove
                </Button>
              </div>
            </div>
            <div className="p-3">
              <p className="type-4 text-headline/90 font-semibold">{selectedImage.name}</p>
              <p className="type-4 text-paragraph/70">
                {(selectedImage.size / 1024 / 1024).toFixed(2)} MB • {selectedImage.type}
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card
          className={`border-2 border-dashed cursor-pointer transition-colors ${
            dragActive 
              ? "border-primary bg-primary/5" 
              : "border-muted-foreground/25 hover:border-primary/50"
          } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <CardContent className="flex flex-col items-center justify-center p-8 text-center">
            <div className="mb-4 p-4 rounded-full bg-muted">
              {dragActive ? (
                <Upload className="h-8 w-8 text-primary animate-bounce" />
              ) : (
                <ImageIcon className="h-8 w-8 text-muted-foreground" />
              )}
            </div>
            
            <h3 className="type-3 text-headline/90 font-semibold mb-2">
              {dragActive ? "Drop image here" : "Upload source image"}
            </h3>
            
            <p className="type-4 text-paragraph/70 mb-4">
              Drag and drop an image, or click to browse
            </p>
            
            <input
              type="file"
              accept="image/jpeg,image/png,image/webp"
              onChange={handleInputChange}
              disabled={disabled}
              className="hidden"
              id="image-upload"
            />
            
            <Button
              asChild
              variant="outline"
              disabled={disabled}
              className="pointer-events-none"
            >
              <label htmlFor="image-upload" className="pointer-events-auto cursor-pointer">
                <Upload className="h-4 w-4 mr-2" />
                Choose Image
              </label>
            </Button>
            
            <p className="type-4 text-paragraph/50 mt-3">
              Supports JPG, PNG, WebP • Max 10MB
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}