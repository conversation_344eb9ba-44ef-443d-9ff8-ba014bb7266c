{"name": "tears-of-the-left", "version": "1.0.0", "description": "Transform your images with AI-powered emotional processing", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.31.5", "@playwright/test": "^1.54.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.26.1", "dexie": "^4.0.11", "framer-motion": "^12.23.12", "lucide-react": "^0.511.0", "next": "latest", "openai": "^5.10.0", "react": "^19.0.0", "react-dom": "^19.0.0", "sharp": "^0.34.3", "sonner": "^2.0.6", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}