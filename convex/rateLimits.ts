import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Get rate limits from environment variables with fallbacks
function getUserDailyLimit(): number {
  const limit = parseInt(process.env.USER_DAILY_LIMIT || '5');
  return (!isNaN(limit) && limit > 0) ? limit : 5;
}

function getGlobalDailyLimit(): number {
  const limit = parseInt(process.env.GLOBAL_DAILY_LIMIT || '1000');
  return (!isNaN(limit) && limit > 0) ? limit : 1000;
}

function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0];
}

export const checkGenerationAllowed = query({
  args: { 
    clerkUserId: v.string(),
    numericUserId: v.number(),
  },
  returns: v.object({
    allowed: v.boolean(),
    userRemaining: v.number(),
    userLimit: v.number(),
    globalRemaining: v.number(),
    globalLimit: v.number(),
    reason: v.optional(v.string()),
  }),
  handler: async (ctx, args) => {
    const today = getCurrentDate();
    
    const userRateLimit = await ctx.db
      .query("rate_limits")
      .withIndex("by_user_id", (q) => q.eq("userId", args.numericUserId))
      .unique();

    let userGenerationsToday = 0;
    if (userRateLimit && userRateLimit.lastResetDate === today) {
      userGenerationsToday = userRateLimit.userGenerationsToday;
    }

    const globalRateLimit = await ctx.db
      .query("global_rate_limits")
      .withIndex("by_date", (q) => q.eq("date", today))
      .unique();

    const globalGenerationsToday = globalRateLimit ? globalRateLimit.totalGenerations : 0;

    const USER_DAILY_LIMIT = getUserDailyLimit();
    const GLOBAL_DAILY_LIMIT = getGlobalDailyLimit();
    
    const userRemaining = Math.max(0, USER_DAILY_LIMIT - userGenerationsToday);
    const globalRemaining = Math.max(0, GLOBAL_DAILY_LIMIT - globalGenerationsToday);

    let allowed = true;
    let reason = undefined;

    if (userRemaining <= 0) {
      allowed = false;
      reason = 'User daily limit exceeded';
    } else if (globalRemaining <= 0) {
      allowed = false;
      reason = 'Global daily limit exceeded';
    }

    return {
      allowed,
      userRemaining,
      userLimit: USER_DAILY_LIMIT,
      globalRemaining,
      globalLimit: GLOBAL_DAILY_LIMIT,
      reason,
    };
  },
});

export const enforceRateLimitAtomic = mutation({
  args: { 
    clerkUserId: v.string(),
    numericUserId: v.number(),
  },
  returns: v.object({
    allowed: v.boolean(),
    userId: v.number(),
    status: v.object({
      userRemaining: v.number(),
      globalRemaining: v.number(),
      userLimit: v.number(),
      globalLimit: v.number(),
      reason: v.optional(v.string()),
    }),
  }),
  handler: async (ctx, args) => {
    const today = getCurrentDate();
    const USER_DAILY_LIMIT = getUserDailyLimit();
    const GLOBAL_DAILY_LIMIT = getGlobalDailyLimit();
    
    let userRateLimit = await ctx.db
      .query("rate_limits")
      .withIndex("by_user_id", (q) => q.eq("userId", args.numericUserId))
      .unique();

    // Atomic upsert for user rate limit
    if (!userRateLimit || userRateLimit.lastResetDate !== today) {
      if (userRateLimit) {
        // Reset existing record
        await ctx.db.patch(userRateLimit._id, {
          userGenerationsToday: 0,
          lastResetDate: today,
          userLimit: USER_DAILY_LIMIT,
        });
        // Re-fetch to get updated data
        userRateLimit = await ctx.db.get(userRateLimit._id);
        if (!userRateLimit) {
          throw new Error("Failed to fetch updated rate limit record");
        }
      } else {
        // Insert new record
        try {
          const newRateLimitId = await ctx.db.insert("rate_limits", {
            userId: args.numericUserId,
            clerkUserId: args.clerkUserId,
            userGenerationsToday: 0,
            lastResetDate: today,
            userLimit: USER_DAILY_LIMIT,
          });
          userRateLimit = await ctx.db.get(newRateLimitId);
          if (!userRateLimit) {
            throw new Error("Failed to create rate limit record");
          }
        } catch (error: any) {
          // Handle race condition where another request created the record
          const existingRateLimit = await ctx.db
            .query("rate_limits")
            .withIndex("by_user_id", (q) => q.eq("userId", args.numericUserId))
            .unique();
          
          if (existingRateLimit && existingRateLimit.lastResetDate === today) {
            userRateLimit = existingRateLimit;
          } else if (existingRateLimit) {
            // Reset if date is different
            await ctx.db.patch(existingRateLimit._id, {
              userGenerationsToday: 0,
              lastResetDate: today,
              userLimit: USER_DAILY_LIMIT,
            });
            userRateLimit = await ctx.db.get(existingRateLimit._id);
            if (!userRateLimit) {
              throw new Error("Failed to fetch updated rate limit record");
            }
          } else {
            throw error; // Re-throw if it's not a race condition
          }
        }
      }
    }

    // Atomic upsert for global rate limit
    let globalRateLimit = await ctx.db
      .query("global_rate_limits")
      .withIndex("by_date", (q) => q.eq("date", today))
      .unique();

    if (!globalRateLimit) {
      try {
        const newGlobalRateLimitId = await ctx.db.insert("global_rate_limits", {
          date: today,
          totalGenerations: 0,
          limit: GLOBAL_DAILY_LIMIT,
        });
        globalRateLimit = await ctx.db.get(newGlobalRateLimitId);
        if (!globalRateLimit) {
          throw new Error("Failed to create global rate limit record");
        }
      } catch (error: any) {
        // Handle race condition where another request created the record
        const existingGlobalRateLimit = await ctx.db
          .query("global_rate_limits")
          .withIndex("by_date", (q) => q.eq("date", today))
          .unique();
        
        if (existingGlobalRateLimit) {
          globalRateLimit = existingGlobalRateLimit;
        } else {
          throw error; // Re-throw if it's not a race condition
        }
      }
    }

    const userRemaining = Math.max(0, USER_DAILY_LIMIT - userRateLimit.userGenerationsToday);
    const globalRemaining = Math.max(0, GLOBAL_DAILY_LIMIT - globalRateLimit.totalGenerations);

    let allowed = true;
    let reason = undefined;

    if (userRemaining <= 0) {
      allowed = false;
      reason = 'User daily limit exceeded';
    } else if (globalRemaining <= 0) {
      allowed = false;
      reason = 'Global daily limit exceeded';
    }

    if (allowed) {
      await ctx.db.patch(userRateLimit._id, {
        userGenerationsToday: userRateLimit.userGenerationsToday + 1,
      });
      
      await ctx.db.patch(globalRateLimit._id, {
        totalGenerations: globalRateLimit.totalGenerations + 1,
      });
    }

    return {
      allowed,
      userId: args.numericUserId,
      status: {
        userRemaining: allowed ? userRemaining - 1 : userRemaining,
        globalRemaining: allowed ? globalRemaining - 1 : globalRemaining,
        userLimit: USER_DAILY_LIMIT,
        globalLimit: GLOBAL_DAILY_LIMIT,
        reason,
      },
    };
  },
});

export const getUserRateLimit = query({
  args: { numericUserId: v.number() },
  returns: v.union(
    v.object({
      userId: v.number(),
      userGenerationsToday: v.number(),
      userLimit: v.number(),
      lastResetDate: v.string(),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    const rateLimit = await ctx.db
      .query("rate_limits")
      .withIndex("by_user_id", (q) => q.eq("userId", args.numericUserId))
      .unique();

    if (!rateLimit) return null;

    return {
      userId: rateLimit.userId,
      userGenerationsToday: rateLimit.userGenerationsToday,
      userLimit: rateLimit.userLimit,
      lastResetDate: rateLimit.lastResetDate,
    };
  },
});

export const getGlobalRateLimit = query({
  args: {},
  returns: v.union(
    v.object({
      date: v.string(),
      totalGenerations: v.number(),
      limit: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx) => {
    const today = getCurrentDate();
    
    return await ctx.db
      .query("global_rate_limits")
      .withIndex("by_date", (q) => q.eq("date", today))
      .unique();
  },
});