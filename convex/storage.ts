import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const logFileUpload = mutation({
  args: {
    clerkUserId: v.string(),
    numericUserId: v.number(),
    fileName: v.string(),
    fileSize: v.number(),
    mimeType: v.string(),
    storageId: v.id("_storage"),
  },
  returns: v.id("file_uploads"),
  handler: async (ctx, args) => {
    return await ctx.db.insert("file_uploads", {
      userId: args.numericUserId,
      fileName: args.fileName,
      fileSize: args.fileSize,
      mimeType: args.mimeType,
      storageId: args.storageId,
      uploadedAt: Date.now(),
    });
  },
});

export const getUserFiles = query({
  args: { 
    numericUserId: v.number(),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("file_uploads"),
      _creationTime: v.number(),
      fileName: v.string(),
      fileSize: v.number(),
      mimeType: v.string(),
      storageId: v.id("_storage"),
      uploadedAt: v.number(),
    })
  ),
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    return await ctx.db
      .query("file_uploads")
      .withIndex("by_user_id", (q) => q.eq("userId", args.numericUserId))
      .order("desc")
      .take(limit);
  },
});

export const getFileUrl = query({
  args: { storageId: v.id("_storage") },
  returns: v.union(v.string(), v.null()),
  handler: async (ctx, args) => {
    return await ctx.storage.getUrl(args.storageId);
  },
});

export const deleteFile = mutation({
  args: { 
    fileUploadId: v.id("file_uploads"),
    numericUserId: v.number(),
  },
  returns: v.boolean(),
  handler: async (ctx, args) => {
    const fileUpload = await ctx.db.get(args.fileUploadId);
    
    if (!fileUpload || fileUpload.userId !== args.numericUserId) {
      throw new Error("File not found or unauthorized");
    }

    await ctx.storage.delete(fileUpload.storageId);
    await ctx.db.delete(args.fileUploadId);
    
    return true;
  },
});

export const getFileMetadata = query({
  args: { 
    storageId: v.id("_storage"),
    numericUserId: v.number(),
  },
  returns: v.union(
    v.object({
      _id: v.id("_storage"),
      _creationTime: v.number(),
      contentType: v.optional(v.string()),
      sha256: v.string(),
      size: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    // First, find the file upload record to verify ownership
    const fileUpload = await ctx.db
      .query("file_uploads")
      .withIndex("by_storage_id", (q) => q.eq("storageId", args.storageId))
      .unique();

    // Check if file exists and user has permission
    if (!fileUpload || fileUpload.userId !== args.numericUserId) {
      return null; // Return null for unauthorized access (consistent with deleteFile)
    }

    // If authorized, return the system metadata
    return await ctx.db.system.get(args.storageId);
  },
});