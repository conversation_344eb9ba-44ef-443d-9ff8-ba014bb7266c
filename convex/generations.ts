import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const logGenerationAttempt = mutation({
  args: {
    clerkUserId: v.string(),
    numericUserId: v.number(),
    prompt: v.string(),
    success: v.boolean(),
    imageUrl: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
    model: v.optional(v.string()),
    processingTime: v.optional(v.number()),
  },
  returns: v.id("generation_logs"),
  handler: async (ctx, args) => {
    // Get the user to update counters
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .unique();

    if (user) {
      // Initialize counters if they don't exist (for existing users)
      const totalGenerations = (user.totalGenerations || 0) + 1;
      const successfulGenerations = (user.successfulGenerations || 0) + (args.success ? 1 : 0);
      const failedGenerations = (user.failedGenerations || 0) + (args.success ? 0 : 1);

      // Update user counters atomically
      await ctx.db.patch(user._id, {
        totalGenerations,
        successfulGenerations,
        failedGenerations,
      });
    }

    // Insert the generation log
    return await ctx.db.insert("generation_logs", {
      clerkUserId: args.clerkUserId,
      userId: args.numericUserId,
      prompt: args.prompt,
      success: args.success,
      imageUrl: args.imageUrl,
      errorMessage: args.errorMessage,
      model: args.model,
      processingTime: args.processingTime,
    });
  },
});

export const getUserGenerationHistory = query({
  args: { 
    clerkUserId: v.string(),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("generation_logs"),
      _creationTime: v.number(),
      prompt: v.string(),
      success: v.boolean(),
      imageUrl: v.optional(v.string()),
      errorMessage: v.optional(v.string()),
      model: v.optional(v.string()),
      processingTime: v.optional(v.number()),
    })
  ),
  handler: async (ctx, args) => {
    // Validate and clamp limit parameter
    let limit = 50; // default
    if (args.limit !== undefined) {
      const parsedLimit = parseInt(String(args.limit));
      if (isNaN(parsedLimit) || parsedLimit < 1) {
        throw new Error("Limit must be a positive integer");
      }
      limit = Math.min(Math.max(parsedLimit, 1), 100); // clamp between 1 and 100
    }
    
    const logs = await ctx.db
      .query("generation_logs")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .order("desc")
      .take(limit);

    return logs.map(log => ({
      _id: log._id,
      _creationTime: log._creationTime,
      prompt: log.prompt,
      success: log.success,
      imageUrl: log.imageUrl,
      errorMessage: log.errorMessage,
      model: log.model,
      processingTime: log.processingTime,
    }));
  },
});

export const getGenerationStats = query({
  args: { clerkUserId: v.string() },
  returns: v.object({
    totalGenerations: v.number(),
    successfulGenerations: v.number(),
    failedGenerations: v.number(),
    successRate: v.number(),
  }),
  handler: async (ctx, args) => {
    // Get the user with counters (O(1) lookup)
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .unique();

    if (!user) {
      return {
        totalGenerations: 0,
        successfulGenerations: 0,
        failedGenerations: 0,
        successRate: 0,
      };
    }

    const totalGenerations = user.totalGenerations || 0;
    const successfulGenerations = user.successfulGenerations || 0;
    const failedGenerations = user.failedGenerations || 0;
    const successRate = totalGenerations > 0 ? (successfulGenerations / totalGenerations) * 100 : 0;

    return {
      totalGenerations,
      successfulGenerations,
      failedGenerations,
      successRate: Math.round(successRate * 100) / 100,
    };
  },
});

export const getTodaysGenerations = query({
  args: { clerkUserId: v.string() },
  returns: v.number(),
  handler: async (ctx, args) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();

    const todaysLogs = await ctx.db
      .query("generation_logs")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .filter((q) => q.gte(q.field("_creationTime"), todayTimestamp))
      .collect();

    // Return total attempts count (not just successful ones) to be consistent with checkGenerationAllowed
    return todaysLogs.length;
  },
});

export const getRecentGenerations = query({
  args: { 
    limit: v.optional(v.number()),
    onlySuccessful: v.optional(v.boolean()),
  },
  returns: v.array(
    v.object({
      _id: v.id("generation_logs"),
      _creationTime: v.number(),
      clerkUserId: v.string(),
      prompt: v.string(),
      success: v.boolean(),
      model: v.optional(v.string()),
    })
  ),
  handler: async (ctx, args) => {
    const limit = args.limit || 100;
    
    let logs;
    
    if (args.onlySuccessful) {
      logs = await ctx.db
        .query("generation_logs")
        .withIndex("by_success", (q) => q.eq("success", true))
        .order("desc")
        .take(limit);
    } else {
      logs = await ctx.db
        .query("generation_logs")
        .order("desc")
        .take(limit);
    }

    return logs.map(log => ({
      _id: log._id,
      _creationTime: log._creationTime,
      clerkUserId: log.clerkUserId,
      prompt: log.prompt,
      success: log.success,
      model: log.model,
    }));
  },
});