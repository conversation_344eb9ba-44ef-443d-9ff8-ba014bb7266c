import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { api } from "./_generated/api";

const http = httpRouter();

http.route({
  path: "/upload",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    
    if (!file) {
      return new Response(JSON.stringify({ error: "No file provided" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    try {
      const storageId = await ctx.storage.store(file);
      
      const clerkUserId = request.headers.get("x-clerk-user-id");
      if (!clerkUserId) {
        return new Response(JSON.stringify({ error: "Unauthorized" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        });
      }

      // Get the numeric user ID for the file upload log
      const numericUserId = await ctx.runQuery(api.auth.getNumericUserId, { clerkUserId });
      if (!numericUserId) {
        return new Response(JSON.stringify({ error: "User not found" }), {
          status: 404,
          headers: { "Content-Type": "application/json" },
        });
      }

      await ctx.runMutation(api.storage.logFileUpload, {
        clerkUserId,
        numericUserId,
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        storageId,
      });

      return new Response(
        JSON.stringify({
          success: true,
          fileId: storageId,
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (error) {
      return new Response(
        JSON.stringify({
          error: "Upload failed",
          details: error instanceof Error ? error.message : "Unknown error",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  }),
});

export default http;