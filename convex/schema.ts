import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    email: v.string(),
    name: v.optional(v.string()),
    clerkUserId: v.string(),
    totalGenerations: v.optional(v.number()),
    successfulGenerations: v.optional(v.number()),
    failedGenerations: v.optional(v.number()),
  }).index("by_clerk_id", ["clerkUserId"]).index("by_email", ["email"]),

  user_mappings: defineTable({
    clerkUserId: v.string(),
    numericId: v.number(),
  }).index("by_clerk_id", ["clerkUserId"])
    .index("by_numeric_id", ["numericId"]),

  generation_logs: defineTable({
    userId: v.number(),
    clerkUserId: v.string(),
    prompt: v.string(),
    success: v.boolean(),
    imageUrl: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
    model: v.optional(v.string()),
    processingTime: v.optional(v.number()),
  }).index("by_user_id", ["userId"])
    .index("by_clerk_user_id", ["clerkUserId"])
    .index("by_success", ["success"]),

  rate_limits: defineTable({
    userId: v.number(),
    clerkUserId: v.string(),
    userGenerationsToday: v.number(),
    lastResetDate: v.string(),
    userLimit: v.number(),
  }).index("by_user_id", ["userId"])
    .index("by_clerk_user_id", ["clerkUserId"]),

  global_rate_limits: defineTable({
    date: v.string(),
    totalGenerations: v.number(),
    limit: v.number(),
  }).index("by_date", ["date"]),

  file_uploads: defineTable({
    userId: v.number(),
    fileName: v.string(),
    fileSize: v.number(),
    mimeType: v.string(),
    storageId: v.id("_storage"),
    uploadedAt: v.number(),
  }).index("by_user_id", ["userId"])
    .index("by_uploaded_at", ["uploadedAt"])
    .index("by_storage_id", ["storageId"]),

  counters: defineTable({
    name: v.string(),
    value: v.number(),
  }).index("by_name", ["name"]),
});