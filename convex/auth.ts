import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createOrUpdateUser = mutation({
  args: {
    clerkUserId: v.string(),
    email: v.string(),
    name: v.optional(v.string()),
  },
  returns: v.id("users"),
  handler: async (ctx, args) => {
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .unique();

    if (existingUser) {
      await ctx.db.patch(existingUser._id, {
        email: args.email,
        name: args.name,
      });
      return existingUser._id;
    } else {
      return await ctx.db.insert("users", {
        clerkUserId: args.clerkUserId,
        email: args.email,
        name: args.name,
        totalGenerations: 0,
        successfulGenerations: 0,
        failedGenerations: 0,
      });
    }
  },
});

export const getUser = query({
  args: { clerkUserId: v.string() },
  returns: v.union(
    v.object({
      _id: v.id("users"),
      _creationTime: v.number(),
      email: v.string(),
      name: v.optional(v.string()),
      clerkUserId: v.string(),
      totalGenerations: v.optional(v.number()),
      successfulGenerations: v.optional(v.number()),
      failedGenerations: v.optional(v.number()),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .unique();
  },
});

export const getUserByEmail = query({
  args: { email: v.string() },
  returns: v.union(
    v.object({
      _id: v.id("users"),
      _creationTime: v.number(),
      email: v.string(),
      name: v.optional(v.string()),
      clerkUserId: v.string(),
      totalGenerations: v.optional(v.number()),
      successfulGenerations: v.optional(v.number()),
      failedGenerations: v.optional(v.number()),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
  },
});

async function getNextNumericId(ctx: any): Promise<number> {
  const counterName = "userIdCounter";
  
  // Try to get the existing counter
  let counter = await ctx.db
    .query("counters")
    .withIndex("by_name", (q: any) => q.eq("name", counterName))
    .unique();

  if (!counter) {
    // Initialize counter if it doesn't exist
    const counterId = await ctx.db.insert("counters", {
      name: counterName,
      value: 1,
    });
    return 1;
  }

  // Atomically increment the counter
  const newValue = counter.value + 1;
  await ctx.db.patch(counter._id, {
    value: newValue,
  });

  return newValue;
}

export const getOrCreateNumericId = mutation({
  args: {
    clerkUserId: v.string(),
  },
  returns: v.number(),
  handler: async (ctx, args) => {
    // Early return if mapping already exists
    const existingMapping = await ctx.db
      .query("user_mappings")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .unique();

    if (existingMapping) {
      return existingMapping.numericId;
    }

    // Retry logic to handle potential race conditions
    const maxRetries = 3;
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const newNumericId = await getNextNumericId(ctx);

        await ctx.db.insert("user_mappings", {
          clerkUserId: args.clerkUserId,
          numericId: newNumericId,
        });

        return newNumericId;
      } catch (error: any) {
        // If this is the last attempt, throw the error
        if (attempt === maxRetries - 1) {
          throw error;
        }
        // Otherwise, retry after a small delay
        await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1)));
      }
    }

    throw new Error("Failed to create numeric ID after multiple retries");
  },
});

export const getNumericUserId = query({
  args: { clerkUserId: v.string() },
  returns: v.union(v.number(), v.null()),
  handler: async (ctx, args) => {
    const mapping = await ctx.db
      .query("user_mappings")
      .withIndex("by_clerk_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .unique();
    
    return mapping ? mapping.numericId : null;
  },
});