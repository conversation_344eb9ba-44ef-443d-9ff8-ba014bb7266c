import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  async headers() {
    return [
      {
        // Apply security headers to all routes
        source: '/(.*)',
        headers: [
          // Content Security Policy
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://*.clerk.accounts.dev https://*.clerk.com https://challenges.cloudflare.com", // Needed for Next.js, React, Clerk, and CAPTCHA
              "style-src 'self' 'unsafe-inline' https://*.clerk.accounts.dev https://*.clerk.com", // Needed for styled components and Clerk
              "img-src 'self' data: blob: https:", // Allow data URLs for base64 images
              "font-src 'self' data: https://*.perplexity.ai https://*.clerk.accounts.dev https://*.clerk.com", // Allow external fonts
              "connect-src 'self' https://api.openai.com https://openrouter.ai https://*.convex.cloud https://*.clerk.accounts.dev https://*.clerk.com",
              "worker-src 'self' blob:", // Allow blob workers for Clerk
              "frame-src 'self' https://*.clerk.accounts.dev https://*.clerk.com https://challenges.cloudflare.com", // Allow Clerk frames and CAPTCHA
              "media-src 'self' data: blob:",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests"
            ].join('; ')
          },
          // HTTP Strict Transport Security
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=********; includeSubDomains; preload'
          },
          // Prevent MIME type sniffing
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          // Prevent clickjacking
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          // XSS Protection
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          // Referrer Policy
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          // Permissions Policy
          {
            key: 'Permissions-Policy',
            value: [
              'camera=()',
              'microphone=()',
              'geolocation=()',
              'payment=()',
              'usb=()',
              'magnetometer=()',
              'gyroscope=()',
              'accelerometer=()'
            ].join(', ')
          },
          // Cross-Origin Resource Policy
          {
            key: 'Cross-Origin-Resource-Policy',
            value: 'same-origin'
          },
          // Cross-Origin Opener Policy
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin'
          },
          // Cross-Origin Embedder Policy
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp'
          }
        ]
      },
      {
        // Additional headers for API routes
        source: '/api/(.*)',
        headers: [
          // Prevent caching of API responses
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate'
          },
          // API specific security headers
          {
            key: 'X-API-Version',
            value: '1.0'
          }
        ]
      }
    ];
  },
  
  // Enable compression for better performance
  compress: true,
  
  // Power by header removal for security
  poweredByHeader: false,
  
  // Additional configuration for production optimization
  reactStrictMode: true
};

export default nextConfig;
