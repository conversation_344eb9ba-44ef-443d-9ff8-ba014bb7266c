# Tears of the Left - AI Image Transformer

Transform images with the "Tears of the Left" effect using AI. This Next.js application uses Convex as the backend database and Clerk for authentication.

## Features

- **AI Image Processing**: Transform images using OpenAI's GPT-4o model through OpenRouter
- **Text Processing**: Convert text into the signature "CheersToTears" style
- **Authentication**: Secure user authentication with Clerk
- **Real-time Database**: Powered by Convex for instant data synchronization
- **Rate Limiting**: Built-in rate limiting to prevent abuse
- **Image Storage**: Local encrypted image storage
- **Responsive Design**: Mobile-friendly interface with dark mode support

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Backend**: Convex (serverless backend)
- **Authentication**: Clerk
- **AI Processing**: OpenRouter API (GPT-4o model)
- **Styling**: Tailwind CSS, shadcn/ui components
- **Image Processing**: Sharp for optimization

## Getting Started

### Prerequisites

- Node.js 18+ or Bun
- A Convex account and project
- A Clerk account and application
- An OpenRouter API key

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd crybaby-2
   ```

2. Install dependencies:
   ```bash
   bun install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```

4. Update the environment variables in `.env.local`:
   ```env
   # Convex (from https://dashboard.convex.dev/)
   NEXT_PUBLIC_CONVEX_URL=your-convex-deployment-url

   # Clerk (from https://dashboard.clerk.com/)
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key
   CLERK_SECRET_KEY=your-clerk-secret-key

   # OpenRouter API Key
   OPENROUTER_API_KEY=your-openrouter-api-key

   # Model Configuration
   MODEL=openai/gpt-4o
   ```

5. Initialize and deploy Convex:
   ```bash
   bunx convex dev
   ```

6. Start the development server:
   ```bash
   bun dev
   ```

## Project Structure

```
app/
├── api/                 # API routes (image processing, text processing)
├── auth/                # Authentication pages (login, sign-up)
├── editor/              # Main editor interface
├── layout.tsx           # Root layout with providers
└── page.tsx             # Landing page

components/
├── ui/                  # shadcn/ui components
├── auth-button.tsx      # Authentication button
├── login-form.tsx       # Login form
├── sign-up-form.tsx     # Sign-up form
└── ...

convex/
├── schema.ts            # Database schema
├── auth.ts              # User management functions
├── generations.ts       # Generation logging
├── rateLimits.ts        # Rate limiting logic
└── storage.ts           # File storage functions

hooks/
├── useConvex.ts         # Convex React hooks
└── useImageStorage.ts   # Image storage hooks

lib/
├── convex.ts            # Convex client configuration
├── envConfig.ts         # Environment configuration
├── imageStorage.ts      # Image storage utilities
└── utils.ts             # Utility functions
```

## Database Schema

The application uses Convex with the following main tables:

- `users` - User profiles linked to Clerk
- `user_mappings` - Maps Clerk IDs to numeric IDs
- `generation_logs` - Tracks all image/text generations
- `rate_limits` - User and global rate limiting
- `file_uploads` - File upload tracking

## Rate Limiting

Built-in atomic rate limiting system:
- User limits: Configurable per-user daily limits
- Global limits: System-wide daily limits
- Atomic operations: Prevents race conditions

## API Endpoints

- `POST /api/process` - Process images with AI
- `POST /api/text-process` - Transform text with AI
- `POST /api/upload` - Upload and validate images

## Development Commands

```bash
bun dev                  # Start development server
bun run build           # Build for production
bun start               # Start production server
bun run lint            # Run ESLint
bunx convex dev         # Start Convex development
```

## Deployment

1. Deploy Convex functions:
   ```bash
   bunx convex deploy --prod
   ```

2. Update production environment variables

3. Deploy to your hosting platform (Vercel recommended)

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Create a Pull Request

## License

This project is licensed under the MIT License.