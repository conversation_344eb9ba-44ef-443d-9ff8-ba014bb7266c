import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { clientEnv, validateClientEnv } from './envConfig';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Environment variable checks using centralized config
export const hasEnvVars = !!(
  clientEnv.convex.url &&
  clientEnv.clerk.publishableKey
);

// Server-side only function for rate limiting validation
export async function validateRateLimitingEnvironment(): Promise<void> {
  // This function should only be called on server-side
  if (typeof window !== 'undefined') {
    throw new Error('validateRateLimitingEnvironment called on client side');
  }
  
  try {
    validateClientEnv();
    // Dynamically import server validation to avoid client-side issues
    const { validateServerEnv } = await import('./envConfig');
    validateServerEnv();
  } catch (error) {
    throw new Error(`Rate limiting environment validation failed: ${error}`);
  }
}
