/**
 * Environment Configuration
 * 
 * This file centralizes environment variable access and provides type safety.
 * It ensures sensitive server-side variables are never exposed to the client.
 */

// Client-side environment variables (safe to expose)
export const clientEnv = (() => {
  const convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;
  const clerkPublishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;

  if (!convexUrl) {
    throw new Error("Missing required client environment variable: NEXT_PUBLIC_CONVEX_URL");
  }
  if (!clerkPublishableKey) {
    throw new Error("Missing required client environment variable: NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY");
  }

  return {
    convex: {
      url: convexUrl,
    },
    clerk: {
      publishableKey: clerkPublishableKey,
    },
    site: {
      url: process.env.NEXT_PUBLIC_SITE_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'),
    },
    upload: {
      maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'),
      allowedTypes: process.env.NEXT_PUBLIC_ALLOWED_TYPES?.split(',') || ['image/jpeg', 'image/png', 'image/webp'],
    },
  } as const;
})();

// Server-side environment variables (NEVER expose to client)
const _serverEnv = (() => {
  // Only validate server env vars on the server side
  if (typeof window !== 'undefined') {
    return {
      clerk: { secretKey: '' },
      openai: { apiKey: '' },
      openrouter: { apiKey: '' },
    };
  }

  const clerkSecretKey = process.env.CLERK_SECRET_KEY;
  const openaiApiKey = process.env.OPENAI_API_KEY;
  const openrouterApiKey = process.env.OPENROUTER_API_KEY;

  if (!clerkSecretKey) {
    throw new Error("Missing required server environment variable: CLERK_SECRET_KEY");
  }
  if (!openaiApiKey) {
    throw new Error("Missing required server environment variable: OPENAI_API_KEY");
  }
  if (!openrouterApiKey) {
    throw new Error("Missing required server environment variable: OPENROUTER_API_KEY");
  }

  return {
    clerk: {
      secretKey: clerkSecretKey,
    },
    openai: {
      apiKey: openaiApiKey,
    },
    openrouter: {
      apiKey: openrouterApiKey,
    },
  };
})();

const _additionalServerConfig = (() => {
  // Only access server env vars on the server side
  if (typeof window !== 'undefined') {
    return {
      model: '',
      imageModel: '',
      nodeEnv: 'development',
      rateLimits: {
        userDailyLimit: 5,
        globalDailyLimit: 1000,
      },
    };
  }

  return {
    model: process.env.MODEL || 'moonshotai/kimi-k2',
    imageModel: process.env.IMAGE_MODEL || 'gpt-image-1',
    nodeEnv: process.env.NODE_ENV || 'development',
    rateLimits: {
      userDailyLimit: parseInt(process.env.USER_DAILY_LIMIT || '5') || 5,
      globalDailyLimit: parseInt(process.env.GLOBAL_DAILY_LIMIT || '1000') || 1000,
    },
  } as const;
})();

const _fullServerEnv = {
  ..._serverEnv,
  ..._additionalServerConfig,
} as const;

// Validation functions
export function validateClientEnv() {
  // The validation is already done in the IIFE above
  // This function now serves as a runtime check that can be called if needed
  const missing: string[] = [];
  
  if (!process.env.NEXT_PUBLIC_CONVEX_URL) missing.push('NEXT_PUBLIC_CONVEX_URL');
  if (!process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY) missing.push('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY');
  
  if (missing.length > 0) {
    throw new Error(`Missing required client environment variables: ${missing.join(', ')}`);
  }
}

// Export serverEnv with client-side protection
export const serverEnv = typeof window !== 'undefined' 
  ? new Proxy({} as typeof _fullServerEnv, {
      get() {
        throw new Error('Attempted to access server environment variables on client side');
      }
    })
  : _fullServerEnv;

export function validateServerEnv() {
  // The validation is already done in the IIFE above
  // This function now serves as a runtime check that can be called if needed
  const missing: string[] = [];
  
  if (!process.env.CLERK_SECRET_KEY) missing.push('CLERK_SECRET_KEY');
  if (!process.env.OPENAI_API_KEY) missing.push('OPENAI_API_KEY');
  if (!process.env.OPENROUTER_API_KEY) missing.push('OPENROUTER_API_KEY');
  
  if (missing.length > 0) {
    throw new Error(`Missing required server environment variables: ${missing.join(', ')}`);
  }
}