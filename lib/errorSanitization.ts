/**
 * Error sanitization utilities to prevent information disclosure
 * while maintaining useful error messages for debugging
 */

interface SanitizedError {
  userMessage: string;
  logMessage: string;
  statusCode: number;
  errorCode?: string;
}

// Sensitive patterns that should not be exposed to users
const SENSITIVE_PATTERNS = [
  // Database connection strings
  /postgresql:\/\/[^@\s]+@[^\/\s]+\/[^\s]+/gi,
  /mysql:\/\/[^@\s]+@[^\/\s]+\/[^\s]+/gi,
  
  // API keys and tokens
  /(?:api[_-]?key|token|secret|password)['":\s]*=?['":\s]*([a-zA-Z0-9_-]{16,})/gi,
  /(?:Bearer|Basic)\s+[a-zA-Z0-9_-]{16,}/gi,
  
  // File paths
  /[A-Za-z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*/g,
  /\/(?:home|root|usr|var|etc|opt)\/[^\s]*/g,
  
  // IP addresses and internal hostnames
  /\b(?:192\.168|10\.|172\.(?:1[6-9]|2\d|3[01]))\.\d{1,3}\.\d{1,3}\b/g,
  /\b(?:localhost|127\.0\.0\.1|0\.0\.0\.0)\b/g,
  
  // Email addresses (partial masking)
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
  
  // Stack traces
  /at\s+[^\s]+\s+\([^)]+\)/g,
  /^\s*at\s+.*$/gm,
  
  // Environment variables
  /process\.env\.[A-Z_]+/g,
  
  // SQL queries
  /(?:SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\s+[^;]+(?:;|$)/gi,
  
  // Version information that might be sensitive
  /node\.js v\d+\.\d+\.\d+/gi,
  /npm\/\d+\.\d+\.\d+/gi
];

// Common error types and their safe user messages
const ERROR_TYPE_MAPPINGS: Record<string, string> = {
  // Database errors
  'connection refused': 'Service temporarily unavailable',
  'connection timeout': 'Service temporarily unavailable',
  'connection reset': 'Service temporarily unavailable',
  'database error': 'Data processing error',
  'syntax error': 'Invalid request format',
  
  // Authentication errors
  'unauthorized': 'Authentication required',
  'invalid token': 'Authentication failed',
  'token expired': 'Session expired',
  'access denied': 'Insufficient permissions',
  
  // Network errors
  'network error': 'Connection error',
  'timeout': 'Request timeout',
  'enotfound': 'Service unavailable',
  'econnrefused': 'Service temporarily unavailable',
  
  // File system errors
  'enoent': 'Resource not found',
  'eacces': 'Access denied',
  'eisdir': 'Invalid file type',
  
  // Rate limiting
  'rate limit': 'Too many requests',
  'quota exceeded': 'Usage limit exceeded',
  
  // Validation errors
  'validation error': 'Invalid input',
  'parse error': 'Invalid data format',
  'schema error': 'Invalid data structure'
};

/**
 * Sanitizes error messages for safe user display
 */
export function sanitizeError(
  error: Error | string | unknown,
  context?: {
    operation?: string;
    userId?: string;
    requestId?: string;
  }
): SanitizedError {
  let originalMessage = '';
  
  // Extract error information
  if (error instanceof Error) {
    originalMessage = error.message;
  } else if (typeof error === 'string') {
    originalMessage = error;
  } else {
    originalMessage = 'Unknown error occurred';
  }
  
  // Create detailed log message (for server logs only)
  const logMessage = `${context?.operation || 'Operation'} failed: ${originalMessage}${
    context?.userId ? ` [User: ${context.userId}]` : ''
  }${context?.requestId ? ` [Request: ${context.requestId}]` : ''}`;
  
  // Start with the original message for sanitization
  const sanitizedMessage = originalMessage.toLowerCase();
  
  // Check for known error types
  let userMessage = 'An unexpected error occurred';
  let statusCode = 500;
  let errorCode: string | undefined;
  
  for (const [pattern, safeMessage] of Object.entries(ERROR_TYPE_MAPPINGS)) {
    if (sanitizedMessage.includes(pattern)) {
      userMessage = safeMessage;
      
      // Set appropriate status codes
      if (pattern.includes('unauthorized') || pattern.includes('token') || pattern.includes('access')) {
        statusCode = 401;
        errorCode = 'AUTH_ERROR';
      } else if (pattern.includes('rate limit') || pattern.includes('quota')) {
        statusCode = 429;
        errorCode = 'RATE_LIMIT_ERROR';
      } else if (pattern.includes('validation') || pattern.includes('parse') || pattern.includes('schema')) {
        statusCode = 400;
        errorCode = 'VALIDATION_ERROR';
      } else if (pattern.includes('not found') || pattern.includes('enoent')) {
        statusCode = 404;
        errorCode = 'NOT_FOUND_ERROR';
      } else if (pattern.includes('network') || pattern.includes('connection') || pattern.includes('timeout')) {
        statusCode = 503;
        errorCode = 'SERVICE_UNAVAILABLE';
      }
      
      break;
    }
  }
  
  // Additional specific error handling
  if (originalMessage.includes('OpenAI') || originalMessage.includes('API')) {
    userMessage = 'AI service temporarily unavailable';
    statusCode = 503;
    errorCode = 'AI_SERVICE_ERROR';
  } else if (originalMessage.includes('Convex') || originalMessage.includes('database')) {
    userMessage = 'Database service temporarily unavailable';
    statusCode = 503;
    errorCode = 'DATABASE_ERROR';
  } else if (originalMessage.includes('sharp') || originalMessage.includes('image')) {
    userMessage = 'Image processing failed';
    statusCode = 400;
    errorCode = 'IMAGE_PROCESSING_ERROR';
  }
  
  return {
    userMessage,
    logMessage,
    statusCode,
    errorCode
  };
}

/**
 * Removes sensitive information from error messages for logging
 */
export function sanitizeErrorForLogging(error: Error | string): string {
  let message = error instanceof Error ? error.message : error;
  
  // Replace sensitive patterns
  for (const pattern of SENSITIVE_PATTERNS) {
    message = message.replace(pattern, '[REDACTED]');
  }
  
  return message;
}

/**
 * Creates a safe error response for API endpoints
 */
export function createSafeErrorResponse(
  error: Error | string | unknown,
  context?: {
    operation?: string;
    userId?: string;
    requestId?: string;
  }
) {
  const sanitized = sanitizeError(error, context);
  
  // Log the full error details securely
  console.error('🔥 Error Details:', {
    operation: context?.operation,
    userId: context?.userId,
    requestId: context?.requestId,
    message: sanitizeErrorForLogging(sanitized.logMessage),
    timestamp: new Date().toISOString()
  });
  
  // Return safe response for client
  return {
    error: sanitized.userMessage,
    code: sanitized.errorCode,
    timestamp: new Date().toISOString(),
    ...(process.env.NODE_ENV === 'development' && {
      debug: sanitizeErrorForLogging(sanitized.logMessage)
    })
  };
}

/**
 * Validates that error messages don't contain sensitive information
 */
export function validateErrorMessage(message: string): {
  isSafe: boolean;
  violations: string[];
} {
  const violations: string[] = [];
  
  for (const pattern of SENSITIVE_PATTERNS) {
    const matches = message.match(pattern);
    if (matches) {
      violations.push(`Sensitive pattern found: ${pattern.toString()}`);
    }
  }
  
  return {
    isSafe: violations.length === 0,
    violations
  };
}

/**
 * Middleware-style error handler for consistent error processing
 */
export function handleApiError(
  error: Error | string | unknown,
  operation: string,
  userId?: string
) {
  const requestId = Math.random().toString(36).substring(2, 15);
  
  const sanitized = sanitizeError(error, {
    operation,
    userId,
    requestId
  });
  
  // Always log errors for monitoring
  console.error(`❌ ${operation} failed:`, {
    requestId,
    userId,
    message: sanitizeErrorForLogging(sanitized.logMessage),
    timestamp: new Date().toISOString(),
    stack: error instanceof Error ? error.stack : undefined
  });
  
  return {
    response: createSafeErrorResponse(error, { operation, userId, requestId }),
    statusCode: sanitized.statusCode
  };
}