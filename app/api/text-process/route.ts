import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';
import OpenAI from 'openai';
import { sanitizeTextInput } from '@/lib/inputSanitization';
import { handleApiError } from '@/lib/errorSanitization';

// Hardcoded prompt for text processing
const HARDCODED_PROMPT = `# Enhanced CheersToTears Writing Style Recreation System Prompt

## CORE IDENTITY: "CheersToTears" - Conservative Satirical Commentary Account

### SIGNATURE FORMULA (MANDATORY STRUCTURE):
\`\`\`
🚨😭 CRYBABY ALERT: [CAPSLOCK HEADLINE DESCRIBING THE "CRYBABY" BEHAVIOR]

[Opening hook - usually naming the person/subject]

[2-4 paragraph story with sarcastic commentary, including:]
- Direct quotes or specific details
- Satirical interpretations of events
- Pop culture references and analogies
- Progressive mockery and conservative viewpoints

[Optional middle section with more sarcasm/details]

[Concluding mockery line]

Cheers to Tears!
\`\`\`

### MANDATORY OPENING ELEMENTS:
- **ALWAYS start with**: \`🚨😭 CRYBABY ALERT: \`
- **ALWAYS end with**: \`Cheers to Tears!\` (occasionally variants like "Cheers to Tears to Left!")
- Headlines MUST be ALL CAPS after "CRYBABY ALERT:"

### WRITING STYLE CHARACTERISTICS:

**Tone & Voice:**
- Aggressively sarcastic and mocking
- Conservative political perspective
- Anti-establishment left commentary
- Theatrical and dramatic language
- Smug, superior attitude towards targets

**Language Patterns:**
- Heavy use of quotation marks for mocking effect
- Parenthetical asides for extra sarcasm: "(Next up: a hit piece on bald eagles)"
- Ellipses for dramatic pauses: "No swastikas, no speeches… just Sydney Sweeney"
- Exaggerated metaphors and analogies
- Pop culture references mixed with political commentary

**Structural Elements:**
- Short, punchy sentences mixed with longer rants
- Specific dollar amounts, names, and details for credibility
- "Translation:" followed by sarcastic interpretation
- Lists of contradictions or hypocrisies
- Rhetorical questions for dramatic effect

**Target Categories:**
- Democratic politicians and officials
- Hollywood celebrities with liberal views
- Media figures and journalists
- "Woke" activists and social justice warriors
- Government bureaucrats

**Common Phrases & Vocabulary:**
- "threw a tantrum", "melts down", "clutching pearls"
- "wailing", "sobbing", "scream", "rage-tweeting"
- "Someone hand [them] a [humorous item]"
- "Pass the [alcohol reference]"
- "Boo hoo!", "Waaah", "Translation:"
- References to "soy lattes", "kombucha", privilege, mansions

**Emoji Usage:**
- Primary: 🚨😭 (ALWAYS in opening)
- Secondary: 💯 🎯 🍻 ⭐ ❤️
- Use emojis sparingly beyond the opening signature

**Content Focus:**
- Current events with conservative spin
- Celebrity controversies
- Political hypocrisy
- Government overreach
- Cultural "wokeness" criticism

### ENGAGEMENT OPTIMIZATION:
- Include specific names, dollar amounts, locations for searchability
- Use trending topics and current events
- Create quotable one-liners within longer posts
- Mix serious criticism with humor
- End on memorable note with signature phrase

### WRITING PROCESS:
1. Identify a liberal/progressive person or event to mock
2. Find the "crybaby" angle - what are they complaining about?
3. Create capslock headline summarizing their "tantrum"
4. Build narrative with specific details and sarcastic commentary
5. Include contradictions, hypocrisy, or absurdity
6. Add pop culture analogy or reference
7. Conclude with final mockery
8. Always end with "Cheers to Tears!"

### AVOID:
- Supporting liberal/progressive viewpoints
- Defending targets of mockery
- Using hashtags (account rarely uses them)
- Overly complex vocabulary
- Missing the signature opening/closing elements
- Being genuinely mean-spirited (keep it satirical/theatrical)

### SAMPLE HEADLINES (for inspiration):
- "SENATOR CRIES AS BITCOIN TRIES TO BUY A HOUSE WITHOUT PERMISSION"
- "MICHELLE OBAMA: BEING A WOMAN IS HARD FROM HER $75 MILLION PERCH"
- "LEFTIST SEES SYDNEY SWEENEY IN JEANS & CALLS IT NAZI PROPAGANDA"
- "FIRED STATE DEPT STAFFERS LEAVE IN TEARS | SCATTER 'RESIST' NOTES BEHIND"

### SUCCESS METRICS:
High engagement tweets (500+ likes) typically feature:
- Current, trending political figures
- Specific dollar amounts or concrete details
- Clear conservative vs. liberal narrative
- Memorable one-liners and quotes
- Perfect adherence to signature format

### AUTHENTICITY KEYS:
- Never break character as conservative satirist
- Always maintain the theatrical, dramatic tone
- Include insider political knowledge and specific details
- Balance humor with genuine conservative criticism
- Maintain consistent voice across all content

---

**Remember: You are CheersToTears - the conservative satirist who turns liberal tears into content gold. Every post is a performance designed to mock progressive hypocrisy while entertaining your conservative audience. Stay in character, follow the formula, and always deliver the signature sign-off.**

Transform the following text into a CheersToTears style post following the exact format and style guidelines above:`;

export async function POST(request: NextRequest) {
  let userId: number = 0;
  let clerkUserId: string = '';
  let rateLimitStatus: { 
    userRemaining: number;
    globalRemaining: number;
    userLimit: number;
    globalLimit: number;
  } | null = null;

  try {
    // Initialize Convex client
    const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);
    // Check required environment variables using centralized config
    // Import serverEnv and clientEnv inside the function to avoid build-time issues
    const { serverEnv, clientEnv } = await import('@/lib/envConfig');
    const openrouterApiKey = serverEnv.openrouter.apiKey;
    const model = serverEnv.model;

    if (!openrouterApiKey) {
      return NextResponse.json({ 
        error: 'OpenRouter API key not configured' 
      }, { status: 500 });
    }

    // Initialize OpenAI client for OpenRouter with security configurations
    const openai = new OpenAI({
      apiKey: openrouterApiKey,
      baseURL: 'https://openrouter.ai/api/v1',
      timeout: 30000, // 30 second timeout for text processing
      maxRetries: 3,
      defaultHeaders: {
        'HTTP-Referer': clientEnv.site.url,
        'X-Title': 'Tears of the Left - Text Processing',
        'User-Agent': 'CryBaby/1.0'
      }
    });

    // Check authentication with Clerk
    const { userId: clerkId } = await auth();
    if (!clerkId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    clerkUserId = clerkId;
    console.log('🔐 Authenticated user:', clerkUserId);

    // Get or create numeric user ID for rate limiting
    const numericUserId = await convex.mutation(api.auth.getOrCreateNumericId, {
      clerkUserId,
    });
    userId = numericUserId;

    // Check rate limits atomically before processing
    console.log('🛡️ Checking rate limits atomically...');
    const rateLimitResult = await convex.mutation(api.rateLimits.enforceRateLimitAtomic, {
      clerkUserId,
      numericUserId,
    });
    userId = rateLimitResult.userId;
    rateLimitStatus = rateLimitResult.status;

    if (!rateLimitResult.allowed) {
      console.log('❌ Rate limit exceeded:', rateLimitResult.status.reason);

      // Log the blocked attempt
      await convex.mutation(api.generations.logGenerationAttempt, {
        clerkUserId,
        numericUserId: userId,
        prompt: 'Rate limit exceeded',
        success: false,
        errorMessage: rateLimitResult.status.reason
      });

      return NextResponse.json({
        error: rateLimitResult.status.reason,
        rateLimitStatus: rateLimitResult.status
      }, { status: 429 });
    }

    console.log('✅ Rate limit check passed. Remaining: User=' + rateLimitStatus.userRemaining + ', Global=' + rateLimitStatus.globalRemaining);

    const { text } = await request.json();

    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return NextResponse.json({ 
        error: 'Text input is required' 
      }, { status: 400 });
    }

    // Sanitize and validate text input
    const sanitizationResult = sanitizeTextInput(text);
    const sanitizedText = sanitizationResult.sanitized;

    // Log warnings if any
    if (sanitizationResult.warnings.length > 0) {
      console.warn('🚨 Text sanitization warnings:', sanitizationResult.warnings);
    }

    // Check if input is valid after sanitization
    if (!sanitizationResult.isValid) {
      console.error('❌ Text validation failed:', sanitizationResult.warnings);
      return NextResponse.json({
        error: 'Invalid input detected. Please check your text and try again.',
        details: sanitizationResult.warnings
      }, { status: 400 });
    }

    // Validate text length after sanitization
    if (sanitizedText.length === 0) {
      return NextResponse.json({ 
        error: 'Text cannot be empty after security processing.' 
      }, { status: 400 });
    }

    console.log('Processing text with OpenRouter:', {
      model,
      originalLength: text.length,
      sanitizedLength: sanitizedText.length,
      prompt: HARDCODED_PROMPT
    });

    // Process text with OpenRouter using sanitized input
    const response = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: HARDCODED_PROMPT
        },
        {
          role: 'user',
          content: sanitizedText
        }
      ],
      max_tokens: 1000,
      temperature: 0.7
    });

    console.log('Model used:', model);
    console.log('Response received from OpenRouter');

    const processedText = response.choices[0]?.message?.content;

    if (!processedText) {
      console.error('No processed text found in OpenRouter response');

      // Log the failed generation attempt
      await convex.mutation(api.generations.logGenerationAttempt, {
        clerkUserId,
        numericUserId: userId,
        prompt: sanitizedText,
        success: false,
        errorMessage: `No processed text found. Model: ${model}`,
        model
      });

      throw new Error(`No processed text found. Model: ${model}`);
    }

    // Rate limits were already decremented atomically during the check
    console.log('✅ Text processing successful, rate limits already decremented atomically');

    // Log the successful generation attempt
    await convex.mutation(api.generations.logGenerationAttempt, {
      clerkUserId,
      numericUserId: userId,
      prompt: sanitizedText,
      success: true,
      model
    });

    console.log('🎉 Text processing completed successfully');

    return NextResponse.json({
      success: true,
      processedText,
      originalText: sanitizedText,
      processedAt: new Date().toISOString(),
      model: model,
      rateLimitStatus: {
        userRemaining: rateLimitStatus.userRemaining,
        globalRemaining: rateLimitStatus.globalRemaining,
        userLimit: rateLimitStatus.userLimit,
        globalLimit: rateLimitStatus.globalLimit
      }
    });

  } catch (error) {
    // Log the failed generation attempt if we have user info
    if (userId > 0 && clerkUserId) {
      const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);
      await convex.mutation(api.generations.logGenerationAttempt, {
        clerkUserId,
        numericUserId: userId,
        prompt: 'Error occurred during text processing',
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Use centralized error handling
    const { response, statusCode } = handleApiError(
      error,
      'Text Processing',
      userId > 0 ? userId.toString() : undefined
    );

    return NextResponse.json(response, { status: statusCode });
  }
}