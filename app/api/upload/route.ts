import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import sharp from 'sharp';
import { validateUploadedFile, sanitizeFileName, MAX_FILE_SIZE } from '@/lib/fileValidation';
import { handleApiError } from '@/lib/errorSanitization';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

export async function POST(request: NextRequest) {
  try {
    // Check authentication with Clerk
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    console.log('🔍 Validating uploaded file:', file.name, file.type, file.size);

    // Comprehensive file validation with magic number verification
    const validationResult = await validateUploadedFile(file);
    
    // Log validation warnings
    if (validationResult.warnings.length > 0) {
      console.warn('⚠️ File validation warnings:', validationResult.warnings);
    }

    // Check if file is valid
    if (!validationResult.isValid) {
      console.error('❌ File validation failed:', validationResult.errors);
      return NextResponse.json({
        error: 'File validation failed',
        details: validationResult.errors,
        warnings: validationResult.warnings
      }, { status: 400 });
    }

    // Additional legacy checks for backward compatibility
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json({ 
        error: 'Invalid file type. Please upload a JPEG, PNG, or WebP image.' 
      }, { status: 400 });
    }

    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json({
        error: 'File too large. Please choose a smaller image.'
      }, { status: 400 });
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Optimize image with sharp
    const optimizedBuffer = await sharp(buffer)
      .resize(2048, 2048, { 
        fit: 'inside', 
        withoutEnlargement: true 
      })
      .jpeg({ quality: 85 })
      .toBuffer();

    // Convert to base64 for API processing
    const base64Image = `data:image/jpeg;base64,${optimizedBuffer.toString('base64')}`;

    // Get image metadata
    const metadata = await sharp(buffer).metadata();

    // Sanitize filename for security
    const sanitizedFilename = sanitizeFileName(file.name);

    console.log('✅ File upload successful:', {
      originalFilename: file.name,
      sanitizedFilename,
      originalSize: file.size,
      optimizedSize: optimizedBuffer.length,
      detectedType: validationResult.detectedType,
      dimensions: `${metadata.width}x${metadata.height}`
    });

    return NextResponse.json({
      success: true,
      image: base64Image,
      metadata: {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: optimizedBuffer.length,
        originalSize: file.size,
        filename: sanitizedFilename,
        detectedType: validationResult.detectedType,
        validationWarnings: validationResult.warnings
      }
    });

  } catch (error) {
    // Use centralized error handling
    const { response, statusCode } = handleApiError(error, 'File Upload');
    return NextResponse.json(response, { status: statusCode });
  }
}