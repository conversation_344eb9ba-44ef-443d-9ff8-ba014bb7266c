import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@/convex/_generated/api';
import { google } from '@ai-sdk/google';
import { generateText } from 'ai';
import sharp from 'sharp';
import { processImageWithLogo } from '@/lib/imageUtils';
import { sanitizeImagePrompt } from '@/lib/inputSanitization';
import { handleApiError } from '@/lib/errorSanitization';
import { serverEnv } from '@/lib/envConfig';
import path from 'path';

export async function POST(request: NextRequest) {
  let userId: number = 0;
  let clerkUserId: string = '';
  let rateLimitStatus: { 
    userRemaining: number;
    globalRemaining: number;
    userLimit: number;
    globalLimit: number;
  } | null = null;

  try {
    // Initialize Convex client
    const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

    // Check for Google AI API key
    if (!serverEnv.google.apiKey) {
      throw new Error("GOOGLE_GENERATIVE_AI_API_KEY environment variable is required");
    }

    // Check authentication with Clerk
    const { userId: clerkId } = await auth();
    if (!clerkId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    clerkUserId = clerkId;
    console.log('🔐 Authenticated user:', clerkUserId);

    // Get or create numeric user ID for rate limiting
    const numericUserId = await convex.mutation(api.auth.getOrCreateNumericId, {
      clerkUserId,
    });
    userId = numericUserId;

    // Check rate limits atomically before processing
    console.log('🛡️ Checking rate limits atomically...');
    const rateLimitResult = await convex.mutation(api.rateLimits.enforceRateLimitAtomic, {
      clerkUserId,
      numericUserId,
    });
    rateLimitStatus = rateLimitResult.status;

    if (!rateLimitResult.allowed) {
      console.log('❌ Rate limit exceeded:', rateLimitResult.status.reason);

      // Log the blocked attempt
      await convex.mutation(api.generations.logGenerationAttempt, {
        clerkUserId,
        numericUserId: userId,
        prompt: 'Rate limit exceeded',
        success: false,
        errorMessage: rateLimitResult.status.reason
      });

      return NextResponse.json({
        error: rateLimitResult.status.reason,
        rateLimitStatus: rateLimitResult.status
      }, { status: 429 });
    }

    console.log('✅ Rate limit check passed. Remaining: User=' + rateLimitStatus.userRemaining + ', Global=' + rateLimitStatus.globalRemaining);

    const { imageData, prompt } = await request.json();

    if (!imageData || !prompt) {
      return NextResponse.json({ 
        error: 'Image data and prompt are required' 
      }, { status: 400 });
    }

    // Sanitize and validate prompt
    const sanitizationResult = sanitizeImagePrompt(prompt);
    const sanitizedPrompt = sanitizationResult.sanitized;

    // Log warnings if any
    if (sanitizationResult.warnings.length > 0) {
      console.warn('🚨 Prompt sanitization warnings:', sanitizationResult.warnings);
    }

    // Check if input is valid after sanitization
    if (!sanitizationResult.isValid) {
      console.error('❌ Input validation failed:', sanitizationResult.warnings);
      return NextResponse.json({
        error: 'Invalid input detected. Please check your prompt and try again.',
        details: sanitizationResult.warnings
      }, { status: 400 });
    }

    // Validate prompt length for gpt-image-1
    if (sanitizedPrompt.length > 32000) {
      return NextResponse.json({
        error: 'Prompt too long. Maximum 32000 characters allowed.'
      }, { status: 400 });
    }

    if (sanitizedPrompt.length === 0) {
      return NextResponse.json({
        error: 'Prompt cannot be empty after security processing.'
      }, { status: 400 });
    }

    // Convert base64 image to buffer
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Convert to suitable format and resize for Gemini
    const processedImageBuffer = await sharp(imageBuffer)
      .resize(1024, 1024, { 
        fit: 'inside', 
        withoutEnlargement: true,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .png()
      .toBuffer();

    // Convert to Uint8Array for Gemini
    const sourceImageBytes = new Uint8Array(processedImageBuffer);

    console.log('Sending image to Gemini 2.5 Flash:', {
      originalSize: imageBuffer.length,
      processedSize: processedImageBuffer.length,
      prompt: sanitizedPrompt,
    });

    // Use Gemini 2.5 Flash for image-to-image generation
    const modelUsed = 'gemini-2.5-flash-image-preview';
    console.log(`Using ${modelUsed} for image transformation...`);
    
    const result = await generateText({
      model: google('gemini-2.5-flash-image-preview'),
      providerOptions: {
        google: { responseModalities: ['TEXT', 'IMAGE'] },
      },
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: sanitizedPrompt,
            },
            {
              type: 'image',
              image: sourceImageBytes,
            },
          ],
        },
      ],
    });

    console.log('Model used:', modelUsed);
    console.log('Gemini response files:', result.files?.length || 0);

    let editedImageUrl = null;
    
    // Check if we got any images from Gemini
    if (result.files && result.files.length > 0) {
      const imageFile = result.files.find(file => file.mediaType.startsWith('image/'));
      
      if (imageFile) {
        console.log(`Generated image: ${imageFile.mediaType}, ${imageFile.uint8Array.length} bytes`);
        
        // Convert Uint8Array to base64 data URL
        const base64String = Buffer.from(imageFile.uint8Array).toString('base64');
        editedImageUrl = `data:${imageFile.mediaType};base64,${base64String}`;
        console.log('Generated data URL from Gemini response');
      }
    }

    if (!editedImageUrl) {
      console.error('No image data found in Gemini response. Response text:', result.text?.substring(0, 200));

      // Log the failed generation attempt
      await convex.mutation(api.generations.logGenerationAttempt, {
        clerkUserId,
        numericUserId: userId,
        prompt: sanitizedPrompt,
        success: false,
        errorMessage: `No image files found in Gemini response. Model: ${modelUsed}`,
        model: modelUsed
      });

      throw new Error(`No image files generated by Gemini. Model: ${modelUsed}. Files returned: ${result.files?.length || 0}`);
    }

    // Rate limits were already decremented atomically during the check
    console.log('✅ Generation successful, rate limits already decremented atomically');

    // Log the successful generation attempt
    await convex.mutation(api.generations.logGenerationAttempt, {
      clerkUserId,
      numericUserId: userId,
      prompt: sanitizedPrompt,
      success: true,
      imageUrl: editedImageUrl,
      model: modelUsed
    });

    console.log('🎉 Generation completed successfully');

    // Add logo overlay to the processed image
    console.log('Adding logo overlay to processed image...');
    const logoPath = path.join(process.cwd(), 'public', 'extended-logo.svg');

    try {
      const imageWithLogo = await processImageWithLogo(editedImageUrl, logoPath, {
        logoSizePercent: 12,  // 12% of image width
        paddingPercent: 3,    // 3% padding from edges
        opacity: 0.9          // 90% opacity for clear visibility
      });

      console.log('Logo overlay added successfully');
      editedImageUrl = imageWithLogo;

    } catch (logoError) {
      console.error('Failed to add logo overlay:', logoError);
      // Continue without logo if overlay fails - don't break the main functionality
      console.log('Continuing without logo overlay due to error');
    }

    return NextResponse.json({
      success: true,
      editedImageUrl,
      originalPrompt: prompt,
      processedAt: new Date().toISOString(),
      model: modelUsed,
      rateLimitStatus: {
        userRemaining: rateLimitStatus.userRemaining,
        globalRemaining: rateLimitStatus.globalRemaining,
        userLimit: rateLimitStatus.userLimit,
        globalLimit: rateLimitStatus.globalLimit
      }
    });

  } catch (error) {
    // Log the failed generation attempt if we have user info
    if (userId > 0 && clerkUserId) {
      const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);
      await convex.mutation(api.generations.logGenerationAttempt, {
        clerkUserId,
        numericUserId: userId,
        prompt: 'Error occurred during processing',
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        model: 'gemini-2.5-flash-image-preview'
      });
    }

    // Use centralized error handling
    const { response, statusCode } = handleApiError(
      error,
      'Image Processing',
      userId > 0 ? userId.toString() : undefined
    );

    return NextResponse.json(response, { status: statusCode });
  }
}