import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import ConvexClientProvider from './ConvexClientProvider';
import "./globals.css";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export const metadata: Metadata = {
  metadataBase: new URL(defaultUrl),
  title: "Tears of the Left",
  description: "Transform your images with AI-powered emotional processing",
  keywords: ["AI", "image processing", "emotion", "transformation", "tears"],
  authors: [{ name: "Tears of the Left" }],
  creator: "Tears of the Left",
  publisher: "Tears of the Left",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/icon-192.png", sizes: "192x192", type: "image/png" },
      { url: "/icon-512.png", sizes: "512x512", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
  },
  manifest: "/site.webmanifest",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: defaultUrl,
    title: "Tears of the Left",
    description: "Transform your images with AI-powered emotional processing",
    siteName: "Tears of the Left",
  },
  twitter: {
    card: "summary_large_image",
    title: "Tears of the Left",
    description: "Transform your images with AI-powered emotional processing",
    creator: "@tearsoftheleft",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  display: "swap",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en" className="w-full h-full">
        <body className={`${geistSans.className} antialiased w-full min-h-screen`}>
          <ConvexClientProvider>
            {children}
          </ConvexClientProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
